{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header <PERSON> -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>
                    <i class="fas fa-clock"></i>
                    Frequência - {{ funcionario.nome_completo }}
                </h1>
                <p>Relatório de registros de ponto e frequência</p>
            </div>
            <div class="header-actions">
                <a href="{{ url_for('funcionarios.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Voltar
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i>
                    Imprimir
                </button>
            </div>
        </div>
    </div>

    <!-- Informações do Funcionário -->
    <div class="funcionario-info-card">
        <div class="funcionario-header">
            <div class="funcionario-avatar">
                {{ funcionario.nome_completo[0].upper() if funcionario.nome_completo else 'F' }}
            </div>
            <div class="funcionario-details">
                <h3>{{ funcionario.nome_completo }}</h3>
                <p><strong>Matrícula:</strong> {{ funcionario.matricula_empresa or 'Não informada' }}</p>
                <p><strong>Cargo:</strong> {{ funcionario.cargo or 'Não informado' }}</p>
                <p><strong>Status:</strong> 
                    <span class="status-badge {% if funcionario.status_cadastro == 'Ativo' %}status-ativo{% else %}status-inativo{% endif %}">
                        {{ funcionario.status_cadastro or 'Inativo' }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Filtros de Data -->
    <div class="filtros-card">
        <h4><i class="fas fa-filter"></i> Filtros</h4>
        <form method="GET" class="filtros-form">
            <div class="form-group">
                <label for="data_inicio">Data Início:</label>
                <input type="date" id="data_inicio" name="data_inicio" value="{{ data_inicio or '' }}" class="form-control">
            </div>
            <div class="form-group">
                <label for="data_fim">Data Fim:</label>
                <input type="date" id="data_fim" name="data_fim" value="{{ data_fim or '' }}" class="form-control">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Filtrar
                </button>
                <a href="{{ url_for('funcionarios.frequencia', funcionario_id=funcionario.id) }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Limpar
                </a>
            </div>
        </form>
    </div>

    <!-- Registros de Ponto -->
    <div class="registros-card">
        <h4><i class="fas fa-list"></i> Registros de Ponto</h4>
        
        {% if registros %}
            <div class="registros-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ registros|length }}</span>
                    <span class="stat-label">Total de Registros</span>
                </div>
            </div>

            <div class="registros-table-container">
                <table class="registros-table">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Hora</th>
                            <th>Tipo</th>
                            <th>Método</th>
                            <th>Status</th>
                            <th>Observações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registro in registros %}
                        <tr>
                            <td>{{ registro.data_registro.strftime('%d/%m/%Y') if registro.data_registro else '-' }}</td>
                            <td>{{ registro.hora_registro.strftime('%H:%M') if registro.hora_registro else '-' }}</td>
                            <td>
                                <span class="tipo-registro tipo-{{ registro.tipo_registro }}">
                                    {% if registro.tipo_registro == 'entrada_manha' %}
                                        Entrada
                                    {% elif registro.tipo_registro == 'saida_almoco' %}
                                        Intervalo
                                    {% elif registro.tipo_registro == 'entrada_tarde' %}
                                        Retorno
                                    {% elif registro.tipo_registro == 'saida' %}
                                        Saída
                                    {% else %}
                                        {{ registro.tipo_registro }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="metodo-registro metodo-{{ registro.metodo_registro }}">
                                    {% if registro.metodo_registro == 'biometrico' %}
                                        <i class="fas fa-fingerprint"></i> Biométrico
                                    {% else %}
                                        <i class="fas fa-keyboard"></i> Manual
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                {% if registro.status_pontualidade %}
                                    <span class="status-pontualidade status-{{ registro.status_pontualidade.lower() }}">
                                        {{ registro.status_pontualidade }}
                                    </span>
                                {% else %}
                                    <span class="status-pontualidade status-normal">Normal</span>
                                {% endif %}
                            </td>
                            <td>{{ registro.observacoes or '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-clock empty-icon"></i>
                <h3>Nenhum registro encontrado</h3>
                <p>Não há registros de ponto para o período selecionado.</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
/* Estilos específicos para a página de frequência */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-info p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.funcionario-info-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.funcionario-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.funcionario-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.funcionario-details h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.funcionario-details p {
    margin: 0.25rem 0;
    color: #666;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-ativo {
    background: #d4edda;
    color: #155724;
}

.status-inativo {
    background: #f8d7da;
    color: #721c24;
}

.filtros-card, .registros-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filtros-card h4, .registros-card h4 {
    margin: 0 0 1rem 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filtros-form {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.form-group label {
    font-weight: 500;
    color: #555;
}

.form-control {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.registros-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
}

.registros-table-container {
    overflow-x: auto;
}

.registros-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.registros-table th,
.registros-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.registros-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.tipo-registro {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tipo-entrada_manha {
    background: #d1ecf1;
    color: #0c5460;
}

.tipo-saida_almoco {
    background: #fff3cd;
    color: #856404;
}

.tipo-entrada_tarde {
    background: #d4edda;
    color: #155724;
}

.tipo-saida {
    background: #f8d7da;
    color: #721c24;
}

.metodo-registro {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.metodo-biometrico {
    color: #28a745;
}

.metodo-manual {
    color: #ffc107;
}

.status-pontualidade {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pontual {
    background: #d4edda;
    color: #155724;
}

.status-atrasado {
    background: #f8d7da;
    color: #721c24;
}

.status-normal {
    background: #e2e3e5;
    color: #383d41;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    margin: 0;
}

/* Estilos para impressão */
@media print {
    .header-actions,
    .filtros-card {
        display: none !important;
    }
    
    .page-header {
        background: #333 !important;
        -webkit-print-color-adjust: exact;
    }
}
</style>
{% endblock %}
