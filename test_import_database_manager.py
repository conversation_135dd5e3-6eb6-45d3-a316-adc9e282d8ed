#!/usr/bin/env python3
"""
Teste específico para verificar importação do DatabaseManager
"""

import sys
import os

# Adicionar o caminho do sistema
sys.path.insert(0, 'var/www/controle-ponto')

def test_import_database_manager():
    """Testa a importação do DatabaseManager"""
    print("🧪 TESTE: IMPORTAÇÃO DO DATABASE MANAGER")
    print("=" * 50)
    
    try:
        print("📦 1. Testando importação do utils.database...")
        from utils.database import DatabaseManager
        print("   ✅ DatabaseManager importado com sucesso")
        
        print("\n📦 2. Testando método execute_query...")
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        print(f"   ✅ execute_query funcionando: {result}")
        
        print("\n📦 3. Testando query de funcionários desligados...")
        funcionarios = DatabaseManager.execute_query("""
            SELECT COUNT(*) as total 
            FROM funcionarios_desligados
        """, fetch_one=True)
        print(f"   ✅ Query funcionários desligados: {funcionarios}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erro de importação: {e}")
        print("   💡 Verifique se o arquivo utils/database.py existe")
        return False
        
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_import_auth():
    """Testa a importação do módulo auth"""
    print("\n🔐 TESTE: IMPORTAÇÃO DO AUTH")
    print("=" * 50)
    
    try:
        print("📦 1. Testando importação do utils.auth...")
        from utils.auth import require_admin
        print("   ✅ require_admin importado com sucesso")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erro de importação: {e}")
        return False
        
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        return False

def test_blueprint_import():
    """Testa a importação do blueprint"""
    print("\n📋 TESTE: IMPORTAÇÃO DO BLUEPRINT")
    print("=" * 50)
    
    try:
        print("📦 1. Testando importação do blueprint...")
        from app_funcionarios_desligados import funcionarios_desligados_bp
        print("   ✅ funcionarios_desligados_bp importado com sucesso")
        
        print(f"   📋 Nome do blueprint: {funcionarios_desligados_bp.name}")
        print(f"   📋 URL prefix: {funcionarios_desligados_bp.url_prefix}")
        
        # Listar rotas do blueprint
        print("\n📋 2. Rotas do blueprint:")
        for rule in funcionarios_desligados_bp.url_map.iter_rules():
            print(f"   - {rule.rule} -> {rule.endpoint}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erro de importação: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False
        
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def verificar_arquivos():
    """Verifica se os arquivos necessários existem"""
    print("\n📁 VERIFICAÇÃO: ARQUIVOS NECESSÁRIOS")
    print("=" * 50)
    
    arquivos = [
        'var/www/controle-ponto/utils/database.py',
        'var/www/controle-ponto/utils/auth.py',
        'var/www/controle-ponto/app_funcionarios_desligados.py',
        'var/www/controle-ponto/templates/funcionarios_desligados/index.html'
    ]
    
    for arquivo in arquivos:
        if os.path.exists(arquivo):
            print(f"   ✅ {arquivo}")
        else:
            print(f"   ❌ {arquivo} - NÃO EXISTE")

if __name__ == "__main__":
    print("🚀 INICIANDO TESTE DE IMPORTAÇÕES")
    print("=" * 60)
    
    # 1. Verificar arquivos
    verificar_arquivos()
    
    # 2. Testar importação do DatabaseManager
    db_ok = test_import_database_manager()
    
    # 3. Testar importação do auth
    auth_ok = test_import_auth()
    
    # 4. Testar importação do blueprint
    blueprint_ok = test_blueprint_import()
    
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES:")
    print(f"   DatabaseManager: {'✅' if db_ok else '❌'}")
    print(f"   Auth: {'✅' if auth_ok else '❌'}")
    print(f"   Blueprint: {'✅' if blueprint_ok else '❌'}")
    
    if db_ok and auth_ok and blueprint_ok:
        print("\n✅ TODOS OS TESTES PASSARAM")
        print("💡 O problema pode estar no servidor Flask ou na sessão")
        print("💡 Verifique se você está logado como admin")
    else:
        print("\n❌ ALGUNS TESTES FALHARAM")
        print("💡 Corrija os problemas de importação primeiro")
