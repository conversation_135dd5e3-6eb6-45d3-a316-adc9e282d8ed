<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 DEBUG - Funcionários Desligados</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="fas fa-bug"></i> DEBUG - Funcionários Desligados</h4>
                    <p>Esta é uma versão simplificada para debug. Se esta página funcionar, o problema está na autenticação ou na versão original.</p>
                </div>

                <!-- Estatísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ total_desligados }}</h5>
                                <p class="card-text">Total Desligados</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ funcionarios_desligados|length }}</h5>
                                <p class="card-text">Exibindo</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">✅</h5>
                                <p class="card-text">Status</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">🔍</h5>
                                <p class="card-text">Debug Mode</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Lista de Funcionários -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users"></i> Funcionários Desligados (Últimos 10)</h5>
                    </div>
                    <div class="card-body">
                        {% if funcionarios_desligados %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID Original</th>
                                            <th>Nome</th>
                                            <th>Matrícula</th>
                                            <th>Data Desligamento</th>
                                            <th>Motivo</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for funcionario in funcionarios_desligados %}
                                        <tr>
                                            <td>{{ funcionario.funcionario_id_original }}</td>
                                            <td>{{ funcionario.nome_completo }}</td>
                                            <td>{{ funcionario.matricula_empresa }}</td>
                                            <td>{{ funcionario.data_desligamento.strftime('%d/%m/%Y %H:%M') if funcionario.data_desligamento else 'N/A' }}</td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    {{ funcionario.motivo_desligamento.replace('_', ' ').title() if funcionario.motivo_desligamento else 'N/A' }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                Nenhum funcionário desligado encontrado.
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Links de Navegação -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-link"></i> Links de Teste</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="/funcionarios-desligados/" class="btn btn-primary w-100">
                                        <i class="fas fa-arrow-right"></i> Página Original
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="/funcionarios-desligados-debug/test" class="btn btn-info w-100">
                                        <i class="fas fa-vial"></i> Teste Simples
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="/" class="btn btn-secondary w-100">
                                        <i class="fas fa-home"></i> Início
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informações de Debug -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Informações de Debug</h5>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li><strong>Total de registros:</strong> {{ total_desligados }}</li>
                                <li><strong>Registros exibidos:</strong> {{ funcionarios_desligados|length }}</li>
                                <li><strong>Template:</strong> funcionarios_desligados/debug.html</li>
                                <li><strong>Blueprint:</strong> funcionarios_desligados_debug</li>
                                <li><strong>URL:</strong> /funcionarios-desligados-debug/</li>
                                <li><strong>Autenticação:</strong> require_login (não require_admin)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
