#!/usr/bin/env python3
"""
Script para testar o ponto manual com autenticação
"""

import requests
import json
import sys

def test_with_login():
    """Testa a API com login"""
    print("🔐 TESTANDO PONTO MANUAL COM AUTENTICAÇÃO")
    print("=" * 50)
    
    base_url = "http://************:5000"
    
    # Criar sessão para manter cookies
    session = requests.Session()
    
    try:
        # 1. Fazer login
        print("🔑 Fazendo login...")
        login_data = {
            'username': 'admin',
            'password': '@Ric6109'
        }
        
        login_response = session.post(
            f"{base_url}/login",
            data=login_data,
            allow_redirects=False
        )
        
        print(f"📊 Login Status: {login_response.status_code}")
        
        if login_response.status_code not in [200, 302]:
            print("❌ Falha no login")
            return False
        
        print("✅ Login realizado com sucesso")
        
        # 2. Testar API de registro manual
        print("🧪 Testando API de registro manual...")
        
        api_data = {
            'funcionario_id': '1',
            'tipo_registro': 'entrada_manha',
            'observacoes': 'Teste com autenticação - correção aplicada'
        }
        
        api_response = session.post(
            f"{base_url}/registro-ponto/api/registrar-manual",
            data=api_data
        )
        
        print(f"📊 API Status: {api_response.status_code}")
        
        try:
            result = api_response.json()
            print(f"📄 Resposta: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print("🎉 SUCESSO: Ponto manual funcionando!")
                return True
            else:
                print(f"❌ ERRO: {result.get('message', 'Erro desconhecido')}")
                
                # Se for erro de funcionário não encontrado, tentar com ID 2
                if 'não encontrado' in result.get('message', '').lower():
                    print("🔄 Tentando com funcionário ID 2...")
                    api_data['funcionario_id'] = '2'
                    
                    api_response2 = session.post(
                        f"{base_url}/registro-ponto/api/registrar-manual",
                        data=api_data
                    )
                    
                    result2 = api_response2.json()
                    print(f"📄 Resposta ID 2: {json.dumps(result2, indent=2, ensure_ascii=False)}")
                    
                    if result2.get('success'):
                        print("🎉 SUCESSO: Ponto manual funcionando com ID 2!")
                        return True
                
                return False
                
        except json.JSONDecodeError:
            print(f"❌ Resposta não é JSON: {api_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def list_funcionarios():
    """Lista funcionários disponíveis"""
    print("👥 LISTANDO FUNCIONÁRIOS DISPONÍVEIS")
    print("=" * 40)
    
    base_url = "http://************:5000"
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': '@Ric6109'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Buscar funcionários
        response = session.get(f"{base_url}/funcionarios/api/listar")
        
        if response.status_code == 200:
            funcionarios = response.json()
            
            if funcionarios.get('success') and funcionarios.get('funcionarios'):
                print("📋 Funcionários ativos:")
                for func in funcionarios['funcionarios'][:5]:  # Primeiros 5
                    print(f"   ID: {func.get('id')} - {func.get('nome_completo', 'N/A')}")
                return True
            else:
                print("❌ Nenhum funcionário encontrado")
                return False
        else:
            print(f"❌ Erro ao buscar funcionários: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 TESTE COMPLETO DO PONTO MANUAL")
    print("=" * 50)
    
    # 1. Listar funcionários
    list_funcionarios()
    print()
    
    # 2. Testar API
    success = test_with_login()
    
    if success:
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ O ponto manual está funcionando corretamente")
        print("🌐 Acesse: http://************:5000/registro-ponto/manual")
    else:
        print("\n❌ TESTE FALHOU!")
        print("🔍 Verifique os logs do servidor para mais detalhes")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
