#!/usr/bin/env python3
"""
Script para ativar jornadas da empresa Ocrim - para executar no servidor
"""

import sys
import os

# Adicionar o caminho do projeto
sys.path.insert(0, '/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager
    print("✅ DatabaseManager importado com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar DatabaseManager: {e}")
    sys.exit(1)

def main():
    print("🔧 ATIVANDO JORNADAS DA EMPRESA OCRIM")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        print("✅ Conexão com banco estabelecida")
        
        # 1. Buscar empresa Ocrim
        print("\n1. Buscando empresa Ocrim...")
        sql_empresa = """
        SELECT id, razao_social, nome_fantasia
        FROM empresas
        WHERE razao_social LIKE %s OR nome_fantasia LIKE %s
        """
        empresas = db.execute_query(sql_empresa, ('%Ocrim%', '%Ocrim%'))
        
        if not empresas:
            print("❌ Empresa Ocrim não encontrada!")
            
            # Listar todas as empresas para debug
            print("\n📋 Empresas disponíveis:")
            todas_empresas = db.execute_query("SELECT id, razao_social FROM empresas LIMIT 10")
            for emp in todas_empresas:
                print(f"   {emp['id']}: {emp['razao_social']}")
            return False
        
        empresa_ocrim = empresas[0]
        print(f"✅ Empresa encontrada: {empresa_ocrim['razao_social']} (ID: {empresa_ocrim['id']})")
        
        # 2. Verificar jornadas existentes
        print(f"\n2. Verificando jornadas da empresa...")
        sql_jornadas = """
        SELECT id, nome_jornada, ativa, padrao
        FROM jornadas_trabalho
        WHERE empresa_id = %s
        ORDER BY padrao DESC, nome_jornada
        """
        jornadas = db.execute_query(sql_jornadas, (empresa_ocrim['id'],))
        
        if not jornadas:
            print("❌ Nenhuma jornada encontrada para a empresa Ocrim!")
            return False
        
        print(f"✅ Encontradas {len(jornadas)} jornadas:")
        jornadas_inativas = []
        
        for jornada in jornadas:
            status = "ATIVA" if jornada['ativa'] else "INATIVA"
            padrao = " (PADRÃO)" if jornada['padrao'] else ""
            print(f"   - {jornada['nome_jornada']}: {status}{padrao}")
            
            if not jornada['ativa']:
                jornadas_inativas.append(jornada)
        
        # 3. Ativar jornadas inativas
        if jornadas_inativas:
            print(f"\n3. Ativando {len(jornadas_inativas)} jornadas inativas...")
            
            for jornada in jornadas_inativas:
                sql_ativar = """
                UPDATE jornadas_trabalho 
                SET ativa = 1, data_atualizacao = NOW()
                WHERE id = %s
                """
                
                try:
                    db.execute_query(sql_ativar, (jornada['id'],))
                    print(f"   ✅ Ativada: {jornada['nome_jornada']}")
                except Exception as e:
                    print(f"   ❌ Erro ao ativar {jornada['nome_jornada']}: {e}")
            
            print(f"\n✅ Processo de ativação concluído!")
            
        else:
            print(f"\n✅ Todas as jornadas já estão ativas!")
        
        # 4. Verificar resultado final
        print(f"\n4. Verificando resultado final...")
        jornadas_finais = db.execute_query(sql_jornadas, (empresa_ocrim['id'],))
        jornadas_ativas_final = [j for j in jornadas_finais if j['ativa']]
        
        print(f"✅ Total de jornadas ativas: {len(jornadas_ativas_final)}")
        
        for jornada in jornadas_finais:
            status = "ATIVA" if jornada['ativa'] else "INATIVA"
            padrao = " (PADRÃO)" if jornada['padrao'] else ""
            print(f"   - {jornada['nome_jornada']}: {status}{padrao}")
        
        if jornadas_ativas_final:
            print("\n🎉 SUCESSO!")
            print("✅ A empresa Ocrim agora tem jornadas ativas!")
            print("✅ A mensagem de fallback não deve mais aparecer!")
        else:
            print("\n❌ PROBLEMA!")
            print("❌ Ainda não há jornadas ativas!")
        
        return len(jornadas_ativas_final) > 0
        
    except Exception as e:
        print(f"❌ Erro durante execução: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
