#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para fazer deploy das correções do espelho de ponto
"""

import paramiko
import os
import sys
from datetime import datetime

def deploy_correcoes():
    """
    Faz deploy das correções do espelho de ponto para o servidor
    """
    
    # Configurações do servidor
    hostname = "************"
    username = "root"
    password = "Rlponto@2024"
    
    # Arquivo local corrigido
    arquivo_local = "var/www/controle-ponto/app_ponto_admin.py"
    arquivo_remoto = "/var/www/controle-ponto/app_ponto_admin.py"
    
    try:
        print("🚀 INICIANDO DEPLOY DAS CORREÇÕES DO ESPELHO DE PONTO")
        print("=" * 60)
        
        # Verificar se arquivo local existe
        if not os.path.exists(arquivo_local):
            print(f"❌ Arquivo local não encontrado: {arquivo_local}")
            return False
        
        # Conectar via SSH
        print(f"🔗 Conectando ao servidor {hostname}...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        # Fazer backup do arquivo atual
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_cmd = f"cp {arquivo_remoto} {arquivo_remoto}_backup_{timestamp}"
        
        print(f"💾 Fazendo backup do arquivo atual...")
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        if stderr.read():
            print(f"⚠️ Aviso no backup: {stderr.read().decode()}")
        
        # Copiar arquivo corrigido
        print(f"📤 Copiando arquivo corrigido para o servidor...")
        sftp = ssh.open_sftp()
        sftp.put(arquivo_local, arquivo_remoto)
        sftp.close()
        
        # Verificar se o arquivo foi copiado corretamente
        print(f"✅ Verificando integridade do arquivo...")
        stdin, stdout, stderr = ssh.exec_command(f"wc -l {arquivo_remoto}")
        linhas_servidor = stdout.read().decode().strip()
        print(f"📊 Linhas no servidor: {linhas_servidor}")
        
        # Verificar se as correções estão presentes
        print(f"🔍 Verificando se as correções foram aplicadas...")
        stdin, stdout, stderr = ssh.exec_command(f'grep -n "CASO ESPECIAL: B1 + B4" {arquivo_remoto}')
        resultado_grep = stdout.read().decode().strip()
        
        if resultado_grep:
            print(f"✅ Correções encontradas no servidor:")
            print(f"   {resultado_grep}")
        else:
            print(f"❌ Correções não encontradas no servidor!")
            return False
        
        # Reiniciar serviços
        print(f"🔄 Reiniciando serviços...")
        
        # Tentar reiniciar nginx
        stdin, stdout, stderr = ssh.exec_command("systemctl restart nginx")
        nginx_error = stderr.read().decode()
        if nginx_error:
            print(f"⚠️ Nginx: {nginx_error}")
        else:
            print(f"✅ Nginx reiniciado com sucesso")
        
        # Tentar reiniciar gunicorn se existir
        stdin, stdout, stderr = ssh.exec_command("systemctl restart gunicorn")
        gunicorn_error = stderr.read().decode()
        if "not found" not in gunicorn_error and gunicorn_error:
            print(f"⚠️ Gunicorn: {gunicorn_error}")
        elif "not found" not in gunicorn_error:
            print(f"✅ Gunicorn reiniciado com sucesso")
        
        # Verificar se há processos Python rodando
        stdin, stdout, stderr = ssh.exec_command("ps aux | grep python | grep -v grep")
        processos = stdout.read().decode()
        if processos:
            print(f"🐍 Processos Python ativos:")
            for linha in processos.strip().split('\n'):
                if 'controle-ponto' in linha:
                    print(f"   {linha}")
        
        ssh.close()
        
        print("\n" + "=" * 60)
        print("🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Acessar: http://************:5000/ponto-admin/")
        print("2. Testar espelho de ponto de um funcionário")
        print("3. Verificar se os cálculos estão corretos:")
        print("   - B1+B4 (jornada sem intervalo)")
        print("   - B1+B2 (apenas manhã)")
        print("   - Dias faltantes aparecem como FALTA")
        print("4. Validar com dados reais")
        
        return True
        
    except paramiko.AuthenticationException:
        print("❌ Erro de autenticação SSH")
        return False
    except paramiko.SSHException as e:
        print(f"❌ Erro SSH: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

if __name__ == "__main__":
    sucesso = deploy_correcoes()
    sys.exit(0 if sucesso else 1)
