#!/usr/bin/env python3
"""
Teste real do problema de funcionários desligados
"""

import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

def test_database_connection():
    """Testa conexão com banco"""
    print("🔍 TESTE 1: CONEXÃO COM BANCO")
    try:
        from utils.database import DatabaseManager
        result = DatabaseManager.execute_query("SELECT 1 as test")
        print(f"   ✅ Conexão OK: {result}")
        return True
    except Exception as e:
        print(f"   ❌ Erro na conexão: {e}")
        return False

def test_table_exists():
    """Testa se a tabela existe"""
    print("\n🔍 TESTE 2: TABELA FUNCIONARIOS_DESLIGADOS")
    try:
        from utils.database import DatabaseManager
        result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        total = result[0]['total'] if result else 0
        print(f"   ✅ Tabela existe com {total} registros")
        return True
    except Exception as e:
        print(f"   ❌ Erro na tabela: {e}")
        return False

def test_blueprint_import():
    """Testa importação do blueprint"""
    print("\n🔍 TESTE 3: IMPORTAÇÃO DO BLUEPRINT")
    try:
        from app_funcionarios_desligados import funcionarios_desligados_bp
        print(f"   ✅ Blueprint importado: {funcionarios_desligados_bp.name}")
        return True
    except Exception as e:
        print(f"   ❌ Erro na importação: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_auth_decorator():
    """Testa o decorador de autenticação"""
    print("\n🔍 TESTE 4: DECORADOR DE AUTENTICAÇÃO")
    try:
        from utils.auth import require_admin
        print(f"   ✅ require_admin importado: {require_admin}")
        return True
    except Exception as e:
        print(f"   ❌ Erro no auth: {e}")
        return False

def test_full_query():
    """Testa a query completa do blueprint"""
    print("\n🔍 TESTE 5: QUERY COMPLETA DO BLUEPRINT")
    try:
        from utils.database import DatabaseManager
        from datetime import datetime, timedelta
        
        query = """
        SELECT
            fd.*,
            u.usuario as usuario_responsavel
        FROM funcionarios_desligados fd
        LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
        WHERE 1=1
        ORDER BY fd.data_desligamento DESC
        """
        
        result = DatabaseManager.execute_query(query)
        print(f"   ✅ Query executada: {len(result) if result else 0} registros")
        
        # Teste das estatísticas
        total_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        total = total_result[0]['total'] if total_result else 0
        print(f"   ✅ Total de desligados: {total}")
        
        return True
    except Exception as e:
        print(f"   ❌ Erro na query: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_template_exists():
    """Testa se o template existe"""
    print("\n🔍 TESTE 6: TEMPLATE")
    try:
        template_path = "/var/www/controle-ponto/templates/funcionarios_desligados/index.html"
        if os.path.exists(template_path):
            print(f"   ✅ Template existe: {template_path}")
            return True
        else:
            print(f"   ❌ Template não existe: {template_path}")
            return False
    except Exception as e:
        print(f"   ❌ Erro no template: {e}")
        return False

def test_route_registration():
    """Testa se a rota está registrada"""
    print("\n🔍 TESTE 7: REGISTRO DA ROTA")
    try:
        # Simular o que o app.py faz
        from flask import Flask
        from app_funcionarios_desligados import funcionarios_desligados_bp
        
        app = Flask(__name__)
        app.register_blueprint(funcionarios_desligados_bp)
        
        # Verificar se a rota existe
        routes = []
        for rule in app.url_map.iter_rules():
            if 'funcionarios-desligados' in rule.rule:
                routes.append(rule.rule)
        
        print(f"   ✅ Rotas encontradas: {routes}")
        return len(routes) > 0
    except Exception as e:
        print(f"   ❌ Erro no registro: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 DIAGNÓSTICO COMPLETO: FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    tests = [
        test_database_connection,
        test_table_exists,
        test_blueprint_import,
        test_auth_decorator,
        test_full_query,
        test_template_exists,
        test_route_registration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ ERRO CRÍTICO no teste: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES:")
    
    test_names = [
        "Conexão com Banco",
        "Tabela Funcionários Desligados", 
        "Importação do Blueprint",
        "Decorador de Autenticação",
        "Query Completa",
        "Template Existe",
        "Registro da Rota"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅" if result else "❌"
        print(f"   {status} {name}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📈 RESULTADO: {passed}/{total} testes passaram")
    
    if passed == total:
        print("✅ TODOS OS TESTES PASSARAM - O problema pode estar na autenticação/sessão")
    else:
        print("❌ ALGUNS TESTES FALHARAM - Verifique os erros acima")
