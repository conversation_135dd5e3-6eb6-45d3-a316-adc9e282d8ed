"""
Blueprint DEBUG para funcionários desligados - versão simplificada
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from utils.auth import require_login  # Mudança: require_login em vez de require_admin
from utils.database import DatabaseManager

# Configuração do logger
logger = logging.getLogger('controle-ponto.funcionarios_desligados_debug')

# Criação do Blueprint DEBUG
funcionarios_desligados_debug_bp = Blueprint('funcionarios_desligados_debug', __name__, url_prefix='/funcionarios-desligados-debug')

@funcionarios_desligados_debug_bp.route('/')
@require_login  # Mudança: apenas login necessário, não admin
def index():
    """
    Página DEBUG de funcionários desligados - versão simplificada.
    """
    try:
        logger.info("🔍 DEBUG: Iniciando carregamento de funcionários desligados")
        
        # Query simplificada
        query = """
        SELECT
            fd.funcionario_id_original,
            fd.nome_completo,
            fd.matricula_empresa,
            fd.data_desligamento,
            fd.motivo_desligamento
        FROM funcionarios_desligados fd
        ORDER BY fd.data_desligamento DESC
        LIMIT 10
        """
        
        logger.info(f"🔍 DEBUG: Executando query: {query}")
        
        # Executar query
        funcionarios_desligados = DatabaseManager.execute_query(query)
        
        logger.info(f"🔍 DEBUG: Funcionários encontrados: {len(funcionarios_desligados) if funcionarios_desligados else 0}")
        
        # Estatísticas simples
        total_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        total_desligados = total_result[0]['total'] if total_result else 0
        
        logger.info(f"🔍 DEBUG: Total de funcionários desligados: {total_desligados}")

        context = {
            'funcionarios_desligados': funcionarios_desligados or [],
            'total_desligados': total_desligados,
            'desligados_mes': 0,  # Simplificado
            'total_demissoes': 0,  # Simplificado
            'total_pedidos': 0,  # Simplificado
            'filtros': {
                'motivo': '',
                'periodo': '',
                'busca': ''
            },
            'debug_mode': True
        }
        
        logger.info("🔍 DEBUG: Context criado com sucesso")
        
        # Usar template simplificado
        return render_template('funcionarios_desligados/debug.html', **context)

    except Exception as e:
        logger.error(f"❌ DEBUG: Erro ao carregar funcionários desligados: {e}")
        import traceback
        logger.error(f"❌ DEBUG: Traceback: {traceback.format_exc()}")
        
        # Retornar página de erro detalhada
        return f"""
        <h1>🔍 DEBUG: Erro nos Funcionários Desligados</h1>
        <h2>Erro:</h2>
        <pre>{str(e)}</pre>
        <h2>Traceback:</h2>
        <pre>{traceback.format_exc()}</pre>
        <p><a href="/">Voltar ao início</a></p>
        """

@funcionarios_desligados_debug_bp.route('/test')
def test():
    """Rota de teste simples"""
    try:
        # Teste básico de conexão
        result = DatabaseManager.execute_query("SELECT 1 as test")
        
        # Teste da tabela
        count_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        
        return f"""
        <h1>🧪 TESTE DEBUG - Funcionários Desligados</h1>
        <h2>✅ Conexão com banco:</h2>
        <pre>{result}</pre>
        
        <h2>✅ Contagem de funcionários desligados:</h2>
        <pre>{count_result}</pre>
        
        <h2>🔗 Links:</h2>
        <ul>
            <li><a href="/funcionarios-desligados-debug/">Página DEBUG completa</a></li>
            <li><a href="/funcionarios-desligados/">Página original</a></li>
            <li><a href="/">Voltar ao início</a></li>
        </ul>
        """
        
    except Exception as e:
        import traceback
        return f"""
        <h1>❌ ERRO NO TESTE</h1>
        <h2>Erro:</h2>
        <pre>{str(e)}</pre>
        <h2>Traceback:</h2>
        <pre>{traceback.format_exc()}</pre>
        """
