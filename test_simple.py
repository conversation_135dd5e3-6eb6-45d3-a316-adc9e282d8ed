#!/usr/bin/env python3

import pymysql

try:
    # Testar conexão direta
    conn = pymysql.connect(
        host='************',
        user='cavalcrod',
        password='200381',
        database='controle_ponto',
        charset='utf8mb4'
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM funcionarios")
    result = cursor.fetchone()
    
    print(f"✅ Conexão OK! Funcionários: {result[0]}")
    
    # Testar estrutura da tabela registros_ponto
    cursor.execute("SHOW COLUMNS FROM registros_ponto")
    columns = cursor.fetchall()
    
    print("\n📋 Colunas da tabela registros_ponto:")
    for col in columns:
        print(f"  - {col[0]}: {col[1]}")
    
    # Verificar se as colunas problemáticas existem
    column_names = [col[0] for col in columns]
    problematic_columns = ['horas_trabalhadas', 'horas_extras', 'banco_horas']
    
    print(f"\n🔍 Verificando colunas problemáticas:")
    for col in problematic_columns:
        exists = col in column_names
        status = "✅ EXISTE" if exists else "❌ NÃO EXISTE"
        print(f"  - {col}: {status}")
    
    conn.close()
    
except Exception as e:
    print(f"❌ Erro: {e}")
    import traceback
    traceback.print_exc()
