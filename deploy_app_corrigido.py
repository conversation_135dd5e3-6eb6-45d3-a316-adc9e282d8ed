#!/usr/bin/env python3
"""
Script para fazer deploy do app.py corrigido
"""

import paramiko
import os
import sys
import time
from datetime import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def deploy_app_corrigido():
    """Faz deploy do app.py corrigido"""
    
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    log_message("🚀 DEPLOY DO APP.PY CORRIGIDO")
    log_message("=" * 50)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        log_message("🔐 Conectando ao servidor...")
        ssh.connect(hostname, username=username, password=password)
        log_message("✅ Conectado com sucesso!")
        
        # Timestamp para backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. Fazer backup do app.py atual
        log_message("📦 Fazendo backup do app.py atual...")
        backup_cmd = f"cp /var/www/controle-ponto/app.py /var/www/controle-ponto/backup-build/app_backup_fix_blueprints_{timestamp}.py"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        
        if stderr.read():
            log_message("⚠️ Aviso no backup, mas continuando...")
        else:
            log_message("✅ Backup do app.py criado")
        
        # 2. Transferir app.py corrigido
        log_message("📤 Transferindo app.py corrigido...")
        sftp = ssh.open_sftp()
        
        local_file = 'var/www/controle-ponto/app.py'
        remote_file = '/var/www/controle-ponto/app.py'
        
        if not os.path.exists(local_file):
            log_message(f"❌ Arquivo local não encontrado: {local_file}")
            return False
        
        sftp.put(local_file, remote_file)
        log_message("✅ app.py transferido com sucesso")
        
        # 3. Transferir app_registro_ponto.py corrigido também
        log_message("📤 Transferindo app_registro_ponto.py corrigido...")
        local_file2 = 'var/www/controle-ponto/app_registro_ponto.py'
        remote_file2 = '/var/www/controle-ponto/app_registro_ponto.py'
        
        if os.path.exists(local_file2):
            sftp.put(local_file2, remote_file2)
            log_message("✅ app_registro_ponto.py transferido")
        
        # 4. Verificar sintaxe Python
        log_message("🐍 Verificando sintaxe do app.py...")
        stdin, stdout, stderr = ssh.exec_command(f'cd /var/www/controle-ponto && python3 -m py_compile app.py')
        syntax_error = stderr.read().decode()
        
        if syntax_error:
            log_message(f"❌ Erro de sintaxe no app.py: {syntax_error}")
            return False
        else:
            log_message("✅ Sintaxe do app.py válida")
        
        # 5. Parar todos os processos Flask
        log_message("🛑 Parando todos os processos Flask...")
        stdin, stdout, stderr = ssh.exec_command('pkill -f "python.*app.py"')
        time.sleep(3)
        
        # 6. Verificar se ainda há processos
        stdin, stdout, stderr = ssh.exec_command('pgrep -f "python.*app.py"')
        running = stdout.read().decode().strip()
        
        if running:
            log_message("🔨 Forçando parada de processos...")
            stdin, stdout, stderr = ssh.exec_command('pkill -9 -f "python.*app.py"')
            time.sleep(2)
        
        # 7. Iniciar o Flask
        log_message("🚀 Iniciando Flask com app.py corrigido...")
        stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && nohup python3 app.py > app.log 2>&1 &')
        time.sleep(8)  # Mais tempo para inicialização
        
        # 8. Verificar se o processo está rodando
        log_message("🔍 Verificando se Flask está rodando...")
        stdin, stdout, stderr = ssh.exec_command('pgrep -f "python.*app.py"')
        new_process = stdout.read().decode().strip()
        
        if new_process:
            log_message(f"✅ Flask iniciado com sucesso (PID: {new_process})")
        else:
            log_message("❌ Falha ao iniciar Flask")
            
            # Verificar logs detalhados
            log_message("📋 Verificando logs detalhados...")
            stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && tail -30 app.log')
            logs = stdout.read().decode()
            log_message(f"📄 Logs completos:\n{logs}")
            return False
        
        # 9. Testar conectividade HTTP
        log_message("🌐 Testando conectividade HTTP...")
        time.sleep(5)
        
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000')
        http_status = stdout.read().decode().strip()
        
        if http_status in ['200', '302']:
            log_message(f"✅ Servidor HTTP respondendo (Status: {http_status})")
        else:
            log_message(f"⚠️ Status HTTP inesperado: {http_status}")
            
            # Verificar logs se não estiver respondendo
            if http_status == '000' or not http_status:
                log_message("📋 Verificando logs por falha HTTP...")
                stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && tail -20 app.log')
                logs = stdout.read().decode()
                log_message(f"📄 Logs HTTP:\n{logs}")
        
        sftp.close()
        ssh.close()
        
        log_message("🎉 DEPLOY CONCLUÍDO!")
        log_message("=" * 50)
        log_message("✅ app.py corrigido e transferido")
        log_message("✅ app_registro_ponto.py atualizado")
        log_message("✅ Flask reiniciado")
        log_message("")
        log_message("🌐 Teste o sistema em: http://************:5000")
        log_message("🔧 Teste o ponto manual em: http://************:5000/registro-ponto/manual")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Erro durante o deploy: {str(e)}")
        return False

def main():
    """Função principal"""
    log_message("RLPONTO-WEB - Deploy de Correção Completa")
    log_message("Desenvolvido por: AiNexus Tecnologia")
    log_message("")
    
    if deploy_app_corrigido():
        log_message("✅ Deploy realizado com sucesso!")
        return True
    else:
        log_message("❌ Falha no deploy!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
