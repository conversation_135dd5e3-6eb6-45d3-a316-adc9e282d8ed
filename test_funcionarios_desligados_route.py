#!/usr/bin/env python3
"""
Teste específico da rota de funcionários desligados
"""

import sys
import os
import pymysql
from pymysql.cursors import DictCursor
import logging
from datetime import datetime, timedelta

# Configuração do banco de dados
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': DictCursor
}

def get_db_connection():
    """Cria conexão com o banco de dados"""
    return pymysql.connect(**DB_CONFIG)

class DatabaseManager:
    @staticmethod
    def execute_query(query, params=None):
        """Executa query no banco"""
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(query, params or ())
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        return result

def testar_rota_funcionarios_desligados():
    """Simula exatamente o que a rota faz"""
    print("🧪 TESTE: SIMULAÇÃO DA ROTA FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    try:
        # Simular os filtros da URL (vazios por padrão)
        motivo_filtro = ''
        periodo_filtro = ''
        busca_filtro = ''

        # Construir query base (exatamente como no blueprint)
        query = """
        SELECT
            fd.*,
            u.usuario as usuario_responsavel
        FROM funcionarios_desligados fd
        LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
        WHERE 1=1
        """
        params = []

        # Aplicar filtros (como no blueprint)
        if motivo_filtro:
            query += " AND fd.motivo_desligamento = %s"
            params.append(motivo_filtro)

        if periodo_filtro:
            dias = int(periodo_filtro)
            data_limite = datetime.now() - timedelta(days=dias)
            query += " AND fd.data_desligamento >= %s"
            params.append(data_limite)

        if busca_filtro:
            query += " AND (fd.nome_completo LIKE %s OR fd.matricula_empresa LIKE %s)"
            params.extend([f'%{busca_filtro}%', f'%{busca_filtro}%'])

        query += " ORDER BY fd.data_desligamento DESC"

        print("📋 1. EXECUTANDO QUERY PRINCIPAL:")
        print(f"   Query: {query}")
        print(f"   Params: {params}")
        
        # Executar query principal
        funcionarios_desligados = DatabaseManager.execute_query(query, params)
        print(f"   ✅ Query executada com sucesso")
        print(f"   📊 Funcionários encontrados: {len(funcionarios_desligados) if funcionarios_desligados else 0}")

        # Calcular estatísticas (como no blueprint)
        print("\n📊 2. CALCULANDO ESTATÍSTICAS:")
        
        # Total de desligados
        total_desligados_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        total_desligados = total_desligados_result[0]['total'] if total_desligados_result else 0
        print(f"   📈 Total de desligados: {total_desligados}")

        # Desligados este mês
        primeiro_dia_mes = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        desligados_mes_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE data_desligamento >= %s",
            (primeiro_dia_mes,)
        )
        desligados_mes = desligados_mes_result[0]['total'] if desligados_mes_result else 0
        print(f"   📈 Desligados este mês: {desligados_mes}")

        # Total de demissões
        total_demissoes_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE motivo_desligamento IN ('Demissao_sem_justa_causa', 'Demissao_com_justa_causa')"
        )
        total_demissoes = total_demissoes_result[0]['total'] if total_demissoes_result else 0
        print(f"   📈 Total de demissões: {total_demissoes}")

        # Total de pedidos de demissão
        total_pedidos_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE motivo_desligamento = 'Pedido_demissao'"
        )
        total_pedidos = total_pedidos_result[0]['total'] if total_pedidos_result else 0
        print(f"   📈 Total de pedidos: {total_pedidos}")

        # Simular o context que seria passado para o template
        context = {
            'funcionarios_desligados': funcionarios_desligados or [],
            'total_desligados': total_desligados,
            'desligados_mes': desligados_mes,
            'total_demissoes': total_demissoes,
            'total_pedidos': total_pedidos,
            'filtros': {
                'motivo': motivo_filtro,
                'periodo': periodo_filtro,
                'busca': busca_filtro
            }
        }

        print("\n📋 3. CONTEXT PARA TEMPLATE:")
        print(f"   ✅ funcionarios_desligados: {len(context['funcionarios_desligados'])} registros")
        print(f"   ✅ total_desligados: {context['total_desligados']}")
        print(f"   ✅ desligados_mes: {context['desligados_mes']}")
        print(f"   ✅ total_demissoes: {context['total_demissoes']}")
        print(f"   ✅ total_pedidos: {context['total_pedidos']}")

        # Mostrar alguns funcionários de exemplo
        if funcionarios_desligados:
            print("\n📋 4. EXEMPLOS DE FUNCIONÁRIOS DESLIGADOS:")
            for i, func in enumerate(funcionarios_desligados[:3]):
                print(f"   {i+1}. {func['nome_completo']} (Matrícula: {func['matricula_empresa']})")
                print(f"      - Data Desligamento: {func['data_desligamento']}")
                print(f"      - Motivo: {func['motivo_desligamento']}")
                print(f"      - Responsável: {func.get('usuario_responsavel', 'N/A')}")

        print("\n" + "=" * 60)
        print("✅ TESTE CONCLUÍDO: Rota funcionando perfeitamente")
        print("💡 O problema pode estar na importação do DatabaseManager no blueprint")
        
        return True

    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def verificar_importacoes():
    """Verifica se as importações do blueprint estão corretas"""
    print("\n🔍 VERIFICANDO IMPORTAÇÕES DO BLUEPRINT:")
    print("-" * 40)
    
    try:
        # Tentar importar os módulos que o blueprint usa
        print("📦 Testando importações:")
        
        # Simular as importações do blueprint
        from datetime import datetime, timedelta
        print("   ✅ datetime importado")
        
        # Verificar se o arquivo utils/database.py existe
        import os
        database_path = "var/www/controle-ponto/utils/database.py"
        if os.path.exists(database_path):
            print("   ✅ utils/database.py existe")
        else:
            print("   ❌ utils/database.py NÃO EXISTE")
        
        # Verificar se o arquivo utils/auth.py existe
        auth_path = "var/www/controle-ponto/utils/auth.py"
        if os.path.exists(auth_path):
            print("   ✅ utils/auth.py existe")
        else:
            print("   ❌ utils/auth.py NÃO EXISTE")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Erro nas importações: {e}")
        return False

if __name__ == "__main__":
    print("🚀 INICIANDO TESTE DA ROTA FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    # 1. Verificar importações
    verificar_importacoes()
    
    # 2. Testar a rota
    testar_rota_funcionarios_desligados()
    
    print("\n💡 PRÓXIMOS PASSOS:")
    print("1. Verificar se o blueprint está registrado no app.py")
    print("2. Verificar se há erros de importação no servidor")
    print("3. Verificar logs do Flask para mais detalhes")
    print("4. Testar a URL diretamente no navegador")
