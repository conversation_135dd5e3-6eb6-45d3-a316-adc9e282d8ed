#!/usr/bin/env python3

import sys
import os

# Adicionar o diretório do app ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

try:
    print("Testando importações...")
    
    # Testar importações básicas
    import pymysql
    print("✅ PyMySQL OK")
    
    from utils.config import Config
    print("✅ Config OK")
    
    from utils.database import get_db_connection
    print("✅ Database OK")
    
    # Testar conexão
    conn = get_db_connection()
    print("✅ Conexão OK")
    conn.close()
    
    # Testar importação do app_registro_ponto
    from app_registro_ponto import registro_ponto_bp
    print("✅ Blueprint registro_ponto OK")
    
    print("\n🎉 Todas as importações funcionaram!")
    
except Exception as e:
    print(f"❌ Erro: {e}")
    import traceback
    traceback.print_exc()
