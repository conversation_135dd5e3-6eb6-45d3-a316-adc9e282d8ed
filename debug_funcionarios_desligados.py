#!/usr/bin/env python3
"""
Debug específico para funcionários desligados
Verifica se a tabela existe e diagnostica problemas
"""

import sys
import os
import pymysql
from pymysql.cursors import DictCursor
import logging

# Configuração do banco de dados
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': DictCursor
}

def get_db_connection():
    """Cria conexão com o banco de dados"""
    return pymysql.connect(**DB_CONFIG)

class DatabaseManager:
    @staticmethod
    def execute_query(query, params=None):
        """Executa query no banco"""
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(query, params or ())
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        return result

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verificar_tabela_funcionarios_desligados():
    """Verifica se a tabela funcionarios_desligados existe"""
    print("🔍 DIAGNÓSTICO: FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    try:
        # 1. Verificar conexão com banco
        print("\n📡 1. TESTANDO CONEXÃO COM BANCO:")
        conn = get_db_connection()
        cursor = conn.cursor()
        print("   ✅ Conexão estabelecida com sucesso")
        
        # 2. Verificar se a tabela existe
        print("\n📋 2. VERIFICANDO SE A TABELA EXISTE:")
        cursor.execute("""
            SELECT COUNT(*) as existe
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'funcionarios_desligados'
        """)
        result = cursor.fetchone()
        tabela_existe = result['existe'] > 0 if result else False
        
        if tabela_existe:
            print("   ✅ Tabela 'funcionarios_desligados' existe")
            
            # 3. Verificar estrutura da tabela
            print("\n📋 3. VERIFICANDO ESTRUTURA DA TABELA:")
            cursor.execute("DESCRIBE funcionarios_desligados")
            colunas = cursor.fetchall()
            print(f"   ✅ Tabela tem {len(colunas)} colunas:")
            
            campos_importantes = [
                'funcionario_id_original', 'nome_completo', 'matricula_empresa', 
                'data_desligamento', 'motivo_desligamento'
            ]
            
            for campo in campos_importantes:
                encontrado = any(col['Field'] == campo for col in colunas)
                status = "✅" if encontrado else "❌"
                print(f"      {status} {campo}")
            
            # 4. Verificar dados na tabela
            print("\n📊 4. VERIFICANDO DADOS NA TABELA:")
            cursor.execute("SELECT COUNT(*) as total FROM funcionarios_desligados")
            total = cursor.fetchone()['total']
            print(f"   📈 Total de registros: {total}")
            
            if total > 0:
                cursor.execute("""
                    SELECT nome_completo, matricula_empresa, data_desligamento 
                    FROM funcionarios_desligados 
                    ORDER BY data_desligamento DESC 
                    LIMIT 5
                """)
                registros = cursor.fetchall()
                print("   📋 Últimos 5 registros:")
                for reg in registros:
                    print(f"      - {reg['nome_completo']} (Matrícula: {reg['matricula_empresa']}) - {reg['data_desligamento']}")
            
        else:
            print("   ❌ Tabela 'funcionarios_desligados' NÃO EXISTE!")
            print("\n🔧 SOLUÇÕES POSSÍVEIS:")
            print("   1. Executar o script de migração: migration_funcionarios_desligados.sql")
            print("   2. Criar a tabela manualmente")
            print("   3. Verificar se o banco está correto")
            
            # Verificar outras tabelas relacionadas
            print("\n📋 VERIFICANDO OUTRAS TABELAS:")
            tabelas_verificar = ['funcionarios', 'usuarios', 'log_desligamentos']
            for tabela in tabelas_verificar:
                cursor.execute(f"""
                    SELECT COUNT(*) as existe
                    FROM information_schema.tables 
                    WHERE table_schema = 'controle_ponto' 
                    AND table_name = '{tabela}'
                """)
                result = cursor.fetchone()
                existe = result['existe'] > 0 if result else False
                status = "✅" if existe else "❌"
                print(f"   {status} Tabela '{tabela}': {'Existe' if existe else 'NÃO EXISTE'}")
        
        cursor.close()
        conn.close()
        return tabela_existe
        
    except Exception as e:
        print(f"\n❌ ERRO NO DIAGNÓSTICO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def testar_blueprint_funcionarios_desligados():
    """Testa o blueprint de funcionários desligados"""
    print("\n🧪 5. TESTANDO BLUEPRINT:")
    print("-" * 40)
    
    try:
        # Simular a query que o blueprint faz
        query = """
        SELECT
            fd.*,
            u.usuario as usuario_responsavel
        FROM funcionarios_desligados fd
        LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
        WHERE 1=1
        ORDER BY fd.data_desligamento DESC
        """
        
        funcionarios_desligados = DatabaseManager.execute_query(query)
        print(f"   ✅ Query do blueprint executada com sucesso")
        print(f"   📊 Resultados encontrados: {len(funcionarios_desligados) if funcionarios_desligados else 0}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na query do blueprint: {e}")
        return False

def criar_tabela_funcionarios_desligados():
    """Cria a tabela funcionarios_desligados se não existir"""
    print("\n🔧 6. CRIANDO TABELA (SE NECESSÁRIO):")
    print("-" * 40)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # SQL para criar a tabela
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS funcionarios_desligados (
            id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
            funcionario_id_original INT UNSIGNED NOT NULL,
            nome_completo VARCHAR(100) NOT NULL,
            cpf VARCHAR(14) NOT NULL,
            rg VARCHAR(20) NOT NULL,
            data_nascimento DATE NOT NULL,
            sexo ENUM('M', 'F', 'Outro') NOT NULL,
            estado_civil ENUM('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
            nacionalidade VARCHAR(50) NOT NULL DEFAULT 'Brasileiro',
            ctps_numero VARCHAR(20) DEFAULT NULL,
            ctps_serie_uf VARCHAR(20) DEFAULT NULL,
            pis_pasep VARCHAR(20) NOT NULL,
            endereco_rua VARCHAR(100) DEFAULT NULL,
            endereco_bairro VARCHAR(50) DEFAULT NULL,
            endereco_cidade VARCHAR(50) DEFAULT NULL,
            endereco_cep VARCHAR(10) NOT NULL,
            endereco_estado VARCHAR(2) NOT NULL,
            telefone1 VARCHAR(15) NOT NULL,
            telefone2 VARCHAR(15) DEFAULT NULL,
            email VARCHAR(100) DEFAULT NULL,
            cargo VARCHAR(100) NOT NULL,
            setor VARCHAR(100) DEFAULT NULL,
            setor_obra VARCHAR(100) DEFAULT NULL,
            matricula_empresa VARCHAR(10) NOT NULL,
            data_admissao DATE NOT NULL,
            tipo_contrato ENUM('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
            nivel_acesso ENUM('Funcionario', 'Supervisao', 'Gerencia') DEFAULT 'Funcionario',
            turno ENUM('Diurno', 'Noturno', 'Misto') NOT NULL,
            tolerancia_ponto INT DEFAULT 10,
            banco_horas BOOLEAN DEFAULT TRUE,
            hora_extra BOOLEAN DEFAULT TRUE,
            status_cadastro ENUM('Ativo', 'Inativo') DEFAULT 'Inativo',
            horas_trabalho_obrigatorias DECIMAL(4,2) DEFAULT 8.00,
            empresa_id INT UNSIGNED DEFAULT NULL,
            jornada_trabalho_id INT UNSIGNED DEFAULT NULL,
            horario_trabalho_id INT UNSIGNED DEFAULT NULL,
            digital_dedo1 LONGBLOB DEFAULT NULL,
            digital_dedo2 LONGBLOB DEFAULT NULL,
            foto_3x4 LONGBLOB DEFAULT NULL,
            data_desligamento TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            motivo_desligamento ENUM(
                'Demissao_sem_justa_causa',
                'Demissao_com_justa_causa', 
                'Pedido_demissao',
                'Termino_contrato',
                'Aposentadoria',
                'Falecimento',
                'Abandono_emprego',
                'Acordo_mutuo',
                'Outros'
            ) NOT NULL,
            observacoes_desligamento TEXT DEFAULT NULL,
            usuario_responsavel_desligamento INT UNSIGNED DEFAULT NULL,
            data_criacao_original TIMESTAMP DEFAULT NULL,
            data_ultima_atualizacao TIMESTAMP DEFAULT NULL,
            INDEX idx_funcionario_original (funcionario_id_original),
            INDEX idx_matricula (matricula_empresa),
            INDEX idx_cpf (cpf),
            INDEX idx_data_desligamento (data_desligamento),
            INDEX idx_motivo (motivo_desligamento),
            INDEX idx_empresa (empresa_id),
            UNIQUE KEY uk_funcionario_original (funcionario_id_original),
            UNIQUE KEY uk_matricula_desligado (matricula_empresa)
        ) ENGINE=InnoDB 
        CHARACTER SET utf8mb4 
        COLLATE utf8mb4_unicode_ci
        COMMENT = 'Histórico de funcionários desligados - Auditoria e Compliance'
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("   ✅ Tabela 'funcionarios_desligados' criada com sucesso")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao criar tabela: {e}")
        return False

if __name__ == "__main__":
    print("🚀 INICIANDO DIAGNÓSTICO DE FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    # 1. Verificar se a tabela existe
    tabela_existe = verificar_tabela_funcionarios_desligados()
    
    # 2. Se não existir, tentar criar
    if not tabela_existe:
        print("\n🔧 TENTANDO CRIAR A TABELA...")
        if criar_tabela_funcionarios_desligados():
            print("✅ Tabela criada! Verificando novamente...")
            tabela_existe = verificar_tabela_funcionarios_desligados()
    
    # 3. Testar o blueprint
    if tabela_existe:
        testar_blueprint_funcionarios_desligados()
    
    print("\n" + "=" * 60)
    if tabela_existe:
        print("✅ DIAGNÓSTICO CONCLUÍDO: Sistema funcionando")
        print("💡 A página de funcionários desligados deve funcionar agora")
    else:
        print("❌ DIAGNÓSTICO CONCLUÍDO: Problemas encontrados")
        print("💡 Execute este script novamente ou contate o administrador")
