# ========================================
# BLUEPRINT REGISTRO DE PONTO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de registro de ponto biométrico e manual
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, time, timedelta
from utils.auth import require_login
from utils.database import get_db_connection
from utils.helpers import mascarar_cpf, validar_cpf_formato, calcular_horas_trabalhadas
from pymysql.cursors import DictCursor
import logging
import ipaddress
import ntplib
import time as systime

# Configurar logging primeiro
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ✅ FUNÇÕES PLACEHOLDER V2.0 - Implementação simplificada
def validar_b5_inicio_extra(funcionario_id, horario_batida):
    """Placeholder para validação B5"""
    return {'permitido': False, 'mensagem': 'B5 temporariamente desabilitado'}

def validar_b6_fim_extra(funcionario_id, horario_batida):
    """Placeholder para validação B6"""
    return {'permitido': False, 'mensagem': 'B6 temporariamente desabilitado'}

def verificar_dia_especial(data_referencia):
    """Placeholder para verificação de dia especial"""
    return {'eh_especial': False, 'tipo': 'NORMAL'}

def criar_solicitacao_aprovacao(*args, **kwargs):
    """Placeholder para criação de aprovação"""
    return None

# Importar novas regras de controle de jornada
try:
    from app_controle_jornada import (
        validar_entrada_manha_nova_regra,
        validar_intervalo_almoco_nova_regra,
        validar_saida_expediente_nova_regra,
        processar_banco_horas_dia
    )
    NOVAS_REGRAS_ATIVAS = True
    logger.info("✅ Novas regras de controle de jornada carregadas")
except ImportError as e:
    NOVAS_REGRAS_ATIVAS = False
    logger.warning(f"⚠️ Novas regras de controle de jornada não disponíveis: {e}")

# Criar Blueprint - Aceitando tanto hífen quanto underline para compatibilidade
registro_ponto_bp = Blueprint('registro_ponto', __name__, url_prefix='/registro-ponto')

# Criar um segundo blueprint com underscore para compatibilidade
registro_ponto_underscore_bp = Blueprint('registro_ponto_underscore', __name__, url_prefix='/registro_ponto')

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def corrigir_encoding_texto(texto):
    """
    Corrige problemas de dupla codificação UTF-8 em textos.

    Args:
        texto (str): Texto que pode ter dupla codificação

    Returns:
        str: Texto com encoding corrigido
    """
    if not texto:
        return texto

    try:
        # Verificar se o texto tem caracteres de dupla codificação
        if any(char in texto for char in ['Ã', 'Â', 'Ç']):
            # Tentar corrigir a dupla codificação
            bytes_originais = texto.encode('latin-1')
            texto_corrigido = bytes_originais.decode('utf-8')
            logger.info(f"🔧 Encoding corrigido: '{texto}' → '{texto_corrigido}'")
            return texto_corrigido
        else:
            # Texto já está correto
            return texto
    except Exception as e:
        # Se não conseguir corrigir, retornar o texto original
        logger.warning(f"⚠️ Não foi possível corrigir encoding de '{texto}': {e}")
        return texto

# ========================================
# NOVA LÓGICA DE DETERMINAÇÃO DE TURNOS
# ========================================

def obter_turnos_disponiveis():
    """
    Obtém os turnos disponíveis da tabela dia_dados.

    Returns:
        list: Lista de turnos com suas configurações
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT
                id, turno, horario_inicio, horario_fim,
                descricao, ordem_prioridade
            FROM dia_dados
            WHERE ativo = TRUE
            ORDER BY ordem_prioridade
        """)

        turnos = cursor.fetchall()
        cursor.close()
        conn.close()

        return turnos

    except Exception as e:
        logger.error(f"Erro ao obter turnos disponíveis: {e}")
        return []

def determinar_turno_por_horario(hora_batida):
    """
    Determina o turno baseado no horário da batida.

    Args:
        hora_batida (time): Horário da batida

    Returns:
        dict: Informações do turno determinado
    """
    try:
        # Converter para string se necessário
        if isinstance(hora_batida, datetime):
            hora_batida = hora_batida.time()
        elif isinstance(hora_batida, str):
            hora_batida = datetime.strptime(hora_batida, '%H:%M:%S').time()

        turnos = obter_turnos_disponiveis()

        for turno in turnos:
            inicio = turno['horario_inicio']
            fim = turno['horario_fim']

            # Caso especial para turno noturno (atravessa meia-noite)
            if turno['turno'] == 'Noite':
                # Noite: 21:00 - 05:00 (dia seguinte)
                if hora_batida >= inicio or hora_batida < fim:
                    return {
                        'turno': turno['turno'],
                        'descricao': turno['descricao'],
                        'horario_inicio': inicio,
                        'horario_fim': fim,
                        'id': turno['id']
                    }
            else:
                # Turnos normais (manhã e tarde)
                if inicio <= hora_batida < fim:
                    return {
                        'turno': turno['turno'],
                        'descricao': turno['descricao'],
                        'horario_inicio': inicio,
                        'horario_fim': fim,
                        'id': turno['id']
                    }

        # Se não encontrou turno específico, retorna manhã como padrão
        return {
            'turno': 'Manha',
            'descricao': 'Turno da manhã (padrão)',
            'horario_inicio': time(5, 0),
            'horario_fim': time(13, 0),
            'id': 1
        }

    except Exception as e:
        logger.error(f"Erro ao determinar turno por horário: {e}")
        return {
            'turno': 'Manha',
            'descricao': 'Turno da manhã (erro)',
            'horario_inicio': time(5, 0),
            'horario_fim': time(13, 0),
            'id': 1
        }

def obter_primeira_batida_do_dia(funcionario_id, data_referencia=None):
    """
    Obtém a primeira batida do funcionário no dia.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Informações da primeira batida ou None
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT
                id, tipo_registro, data_hora, metodo_registro
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            ORDER BY data_hora ASC
            LIMIT 1
        """, (funcionario_id, data_referencia))

        primeira_batida = cursor.fetchone()
        cursor.close()
        conn.close()

        return primeira_batida

    except Exception as e:
        logger.error(f"Erro ao obter primeira batida do dia: {e}")
        return None

def determinar_turno_funcionario(funcionario_id, data_referencia=None):
    """
    Determina o turno do funcionário baseado na primeira batida do dia.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Informações do turno determinado
    """
    try:
        primeira_batida = obter_primeira_batida_do_dia(funcionario_id, data_referencia)

        if primeira_batida:
            # Usar horário da primeira batida para determinar turno
            hora_batida = primeira_batida['data_hora'].time()
            turno_info = determinar_turno_por_horario(hora_batida)
            turno_info['baseado_em_batida'] = True
            turno_info['primeira_batida'] = primeira_batida
            return turno_info
        else:
            # Se não há batidas, usar horário atual para sugerir turno
            hora_atual = datetime.now().time()
            turno_info = determinar_turno_por_horario(hora_atual)
            turno_info['baseado_em_batida'] = False
            turno_info['primeira_batida'] = None
            return turno_info

    except Exception as e:
        logger.error(f"Erro ao determinar turno do funcionário: {e}")
        return {
            'turno': 'Manha',
            'descricao': 'Turno da manhã (erro)',
            'horario_inicio': time(5, 0),
            'horario_fim': time(13, 0),
            'id': 1,
            'baseado_em_batida': False,
            'primeira_batida': None
        }

# ========================================
# SISTEMA DE INFERÊNCIA DE BATIDAS
# ========================================

def obter_batidas_do_dia(funcionario_id, data_referencia=None):
    """
    Obtém todas as batidas do funcionário no dia ordenadas por horário.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        list: Lista de batidas ordenadas por horário
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT
                id, tipo_registro, data_hora, metodo_registro,
                observacoes, status_pontualidade
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            ORDER BY data_hora ASC
        """, (funcionario_id, data_referencia))

        batidas = cursor.fetchall()
        cursor.close()
        conn.close()

        return batidas

    except Exception as e:
        logger.error(f"Erro ao obter batidas do dia: {e}")
        return []

def obter_horarios_funcionario(funcionario_id):
    """
    Obtém os horários de trabalho configurados para o funcionário.
    CORREÇÃO: Busca horários diretamente da tabela funcionarios primeiro.

    Args:
        funcionario_id (int): ID do funcionário

    Returns:
        dict: Horários de trabalho ou horários padrão
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # CORREÇÃO 1: Buscar horários diretamente do funcionário primeiro
            cursor.execute("""
                SELECT
                    f.id as funcionario_id,
                    f.nome,
                    f.empresa_id,
                    e.razao_social,
                    f.jornada_seg_qui_entrada,
                    f.jornada_seg_qui_saida,
                    f.jornada_sex_entrada,
                    f.jornada_sex_saida,
                    f.jornada_intervalo_entrada,
                    f.jornada_intervalo_saida,
                    f.tolerancia_ponto
                FROM funcionarios f
                LEFT JOIN empresas e ON f.empresa_id = e.id
                WHERE f.id = %s AND f.ativo = TRUE
            """, (funcionario_id,))

            resultado = cursor.fetchone()

            if resultado and resultado['jornada_seg_qui_entrada']:
                # Horários encontrados na tabela funcionarios
                logger.info(f"[HORARIOS] Horários encontrados na tabela funcionarios para ID {funcionario_id}")
                return {
                    'funcionario_id': resultado['funcionario_id'],
                    'nome': resultado['nome'],
                    'empresa_id': resultado['empresa_id'],
                    'entrada_manha': resultado['jornada_seg_qui_entrada'],
                    'saida_almoco': resultado['jornada_intervalo_entrada'],
                    'entrada_tarde': resultado['jornada_intervalo_saida'],
                    'saida': resultado['jornada_seg_qui_saida'],
                    'tolerancia_minutos': resultado['tolerancia_ponto'] or 10,
                    'origem': 'funcionario'
                }

            # FALLBACK: Buscar na tabela horarios_trabalho
            elif resultado:
                cursor.execute("""
                    SELECT
                        ht.entrada_manha,
                        ht.saida_almoco,
                        ht.entrada_tarde,
                        ht.saida,
                        ht.tolerancia_minutos
                    FROM horarios_trabalho ht
                    WHERE ht.empresa_id = %s AND ht.ativo = 1
                    ORDER BY ht.id DESC
                    LIMIT 1
                """, (resultado['empresa_id'],))

                horario_empresa = cursor.fetchone()

                if horario_empresa:
                    logger.info(f"[HORARIOS] Horários encontrados na tabela horarios_trabalho para ID {funcionario_id}")
                    return {
                        'funcionario_id': resultado['funcionario_id'],
                        'nome': resultado['nome'],
                        'empresa_id': resultado['empresa_id'],
                        'entrada_manha': horario_empresa['entrada_manha'],
                        'saida_almoco': horario_empresa['saida_almoco'],
                        'entrada_tarde': horario_empresa['entrada_tarde'],
                        'saida': horario_empresa['saida'],
                        'tolerancia_minutos': horario_empresa['tolerancia_minutos'] or 10,
                        'origem': 'empresa'
                    }

            # FALLBACK FINAL: Horários padrão
            logger.warning(f"[HORARIOS] Usando horários padrão para funcionário {funcionario_id}")
            return {
                'funcionario_id': funcionario_id,
                'nome': 'Funcionário',
                'empresa_id': 1,
                'entrada_manha': '07:00',
                'saida_almoco': '12:00',
                'entrada_tarde': '13:00',
                'saida': '17:00',
                'tolerancia_minutos': 10,
                'origem': 'padrao'
            }

    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {e}")
        # Retornar horários padrão em caso de erro
        return {
            'funcionario_id': funcionario_id,
            'nome': 'Funcionário',
            'empresa_id': 1,
            'entrada_manha': '07:00',
            'saida_almoco': '12:00',
            'entrada_tarde': '13:00',
            'saida': '17:00',
            'tolerancia_minutos': 10,
            'origem': 'erro'
        }
    finally:
        if 'connection' in locals():
            connection.close()

def validar_sequencia_registros(funcionario_id, data, tipo_registro):
    """
    NOVA FUNÇÃO: Valida se o tipo de registro está na sequência correta.

    Args:
        funcionario_id (int): ID do funcionário
        data (str): Data do registro (YYYY-MM-DD)
        tipo_registro (str): Tipo do registro a ser validado

    Returns:
        tuple: (bool, str) - (é_válido, mensagem)
    """
    try:
        # Obter registros já existentes do dia
        registros_dia = obter_batidas_do_dia(funcionario_id, data)
        tipos_registrados = [r['tipo_registro'] for r in registros_dia]

        # Definir sequência obrigatória
        sequencia_obrigatoria = [
            'entrada_manha',      # B1
            'saida_almoco',       # B2
            'entrada_tarde',      # B3
            'saida'               # B4
        ]

        # Validações específicas por tipo
        if tipo_registro == 'entrada_manha':
            # Primeira batida do dia - sempre permitida
            if 'entrada_manha' in tipos_registrados:
                return False, "Entrada da manhã já foi registrada hoje"
            return True, "Entrada da manhã autorizada"

        elif tipo_registro == 'saida_almoco':
            # Precisa ter entrada_manha
            if 'entrada_manha' not in tipos_registrados:
                return False, "Entrada da manhã obrigatória antes da saída para almoço"
            if 'saida_almoco' in tipos_registrados:
                return False, "Saída para almoço já foi registrada hoje"
            return True, "Saída para almoço autorizada"

        elif tipo_registro == 'entrada_tarde':
            # Precisa ter saida_almoco
            if 'saida_almoco' not in tipos_registrados:
                return False, "Saída para almoço obrigatória antes da entrada da tarde"
            if 'entrada_tarde' in tipos_registrados:
                return False, "Entrada da tarde já foi registrada hoje"
            return True, "Entrada da tarde autorizada"

        elif tipo_registro == 'saida':
            # Precisa ter entrada_tarde OU entrada_manha (caso não tenha almoço)
            if 'entrada_tarde' not in tipos_registrados and 'entrada_manha' not in tipos_registrados:
                return False, "Entrada obrigatória antes da saída"
            if 'saida' in tipos_registrados:
                return False, "Saída já foi registrada hoje"
            return True, "Saída autorizada"

        elif tipo_registro in ['inicio_extra', 'fim_extra']:
            # B5/B6 - Precisa ter jornada completa
            if 'saida' not in tipos_registrados:
                return False, f"{tipo_registro} só permitido após completar a jornada normal"
            return True, f"{tipo_registro} autorizado"

        else:
            # Tipo não reconhecido
            return False, f"Tipo de registro '{tipo_registro}' não reconhecido"

    except Exception as e:
        logger.error(f"Erro ao validar sequência: {e}")
        return False, f"Erro na validação: {str(e)}"

def classificar_batida_por_sequencia(numero_batida, turno_info):
    """
    Classifica o tipo de batida baseado na sequência e turno.

    Args:
        numero_batida (int): Número da batida (1, 2, 3, 4)
        turno_info (dict): Informações do turno

    Returns:
        str: Tipo de registro inferido
    """
    # Mapeamento padrão de sequência
    sequencia_padrao = {
        1: 'entrada_manha',    # Primeira batida = entrada
        2: 'saida_almoco',     # Segunda batida = início intervalo
        3: 'entrada_tarde',    # Terceira batida = retorno intervalo
        4: 'saida'             # Quarta batida = saída
    }

    # Para turno da tarde, ajustar primeira batida
    if turno_info['turno'] == 'Tarde' and numero_batida == 1:
        return 'entrada_tarde'

    # Para turno da noite, ajustar primeira batida
    if turno_info['turno'] == 'Noite' and numero_batida == 1:
        return 'entrada_manha'  # Manter como entrada_manha para compatibilidade

    return sequencia_padrao.get(numero_batida, 'entrada_manha')

def classificar_batida_inteligente(funcionario_id, numero_batida, turno_info, hora_atual=None):
    """
    Classifica o tipo de batida baseado na sequência, turno E horário atual.
    ✅ V2.0: Suporte para B5/B6 (horas extras)

    Args:
        funcionario_id (int): ID do funcionário
        numero_batida (int): Número da batida (1, 2, 3, 4, 5, 6)
        turno_info (dict): Informações do turno
        hora_atual (datetime, optional): Horário atual. Defaults to now().

    Returns:
        str: Tipo de registro inferido
    """
    try:
        if hora_atual is None:
            hora_atual = datetime.now()

        logger.info(f"🎯 [CLASSIFICAÇÃO INTELIGENTE] INICIANDO - Funcionário: {funcionario_id}, Batida: #{numero_batida}, Hora: {hora_atual.strftime('%H:%M:%S')}")

        # Obter horários de trabalho do funcionário
        horarios = obter_horarios_funcionario(funcionario_id)
        if not horarios:
            # ✅ CORREÇÃO: Não permitir registro se funcionário não tem horário configurado
            logger.error(f"❌ Funcionário {funcionario_id} não possui horário de trabalho configurado")
            return None  # Retorna None para indicar erro

        entrada_manha = horarios.get('entrada_manha')
        saida_almoco = horarios.get('saida_almoco')
        entrada_tarde = horarios.get('entrada_tarde')
        saida = horarios.get('saida')

        # Converter para objetos time se necessário
        if isinstance(hora_atual, datetime):
            hora_atual_obj = hora_atual.time()
        else:
            hora_atual_obj = hora_atual

        # ✅ NOVA REGRA: Determinar período baseado na tabela dia_dados
        periodo_atual = None
        tipo_sugerido = None

        try:
            # Buscar período atual na tabela dia_dados
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)

            cursor.execute("""
                SELECT turno, horario_inicio, horario_fim, descricao
                FROM dia_dados
                WHERE ativo = TRUE
                ORDER BY ordem_prioridade
            """)

            periodos = cursor.fetchall()
            cursor.close()
            conn.close()

            # Determinar período baseado no horário atual
            for periodo in periodos:
                inicio = periodo['horario_inicio']
                fim = periodo['horario_fim']
                turno = periodo['turno']

                # Caso especial para períodos noturnos que atravessam meia-noite
                if turno.startswith('Noite_'):
                    if turno == 'Noite_Inicio' and hora_atual_obj >= inicio:
                        periodo_atual = turno
                        tipo_sugerido = "entrada_manha"
                        break
                    elif turno == 'Noite_Intervalo' and hora_atual_obj < fim:
                        periodo_atual = turno
                        tipo_sugerido = "saida_almoco"
                        break
                    elif turno == 'Noite_Fim' and inicio <= hora_atual_obj < fim:
                        periodo_atual = turno
                        tipo_sugerido = "entrada_tarde"
                        break
                else:
                    # Períodos normais (diurnos)
                    if inicio <= hora_atual_obj < fim:
                        periodo_atual = turno
                        # Mapear período para tipo de registro
                        if turno == 'Manha':
                            tipo_sugerido = "entrada_manha"
                        elif turno == 'Intervalo':
                            tipo_sugerido = "saida_almoco"
                        elif turno == 'Tarde':
                            tipo_sugerido = "entrada_tarde"
                        elif turno == 'Fim_Diurno':
                            tipo_sugerido = "saida"
                        break

            # Se não encontrou período, usar padrão
            if not periodo_atual:
                periodo_atual = "indefinido"
                tipo_sugerido = None

        except Exception as e:
            logger.error(f"Erro ao consultar tabela dia_dados: {e}")
            periodo_atual = "indefinido"
            tipo_sugerido = None

        logger.info(f"[CLASSIFICAÇÃO INTELIGENTE] Funcionário: {funcionario_id}, "
                   f"Batida: #{numero_batida}, Hora: {hora_atual_obj}, Período: {periodo_atual}, Tipo sugerido: {tipo_sugerido}")

        # ✅ CORREÇÃO CRÍTICA: PRIORIZAR PERÍODO SOBRE SEQUÊNCIA
        # Se período foi determinado pela tabela dia_dados, usar SEMPRE
        if tipo_sugerido and periodo_atual != "indefinido":
            # Obter registros existentes do dia
            data_atual = date.today()
            registros_existentes = obter_batidas_do_dia(funcionario_id, data_atual)
            tipos_registrados = [r['tipo_registro'] for r in registros_existentes]

            # Verificar se já existe registro do tipo sugerido
            if tipo_sugerido in tipos_registrados:
                logger.warning(f"[PERÍODO] Tipo {tipo_sugerido} já registrado. Usando fallback de sequência.")
                # Usar fallback de sequência quando tipo do período já foi registrado
            else:
                logger.info(f"[PERÍODO] ✅ USANDO TIPO BASEADO NO PERÍODO: {tipo_sugerido}")
                return tipo_sugerido

        # ✅ FALLBACK INTELIGENTE: Lógica baseada em sequência quando período falha
        logger.info(f"[FALLBACK] Usando lógica de sequência - Motivo: Período={periodo_atual}, Tipo sugerido={tipo_sugerido}")

        # Obter registros existentes do dia para determinar próximo tipo
        data_atual = date.today()
        registros_existentes = obter_batidas_do_dia(funcionario_id, data_atual)
        tipos_registrados = [r['tipo_registro'] for r in registros_existentes]

        logger.info(f"[FALLBACK] Tipos já registrados: {tipos_registrados}")
        logger.info(f"[FALLBACK] Número da batida: {numero_batida}")

        # ✅ LÓGICA DE SEQUÊNCIA: Determinar próximo tipo baseado no que falta

        # 1. Se não tem entrada da manhã, sempre oferecer entrada_manha
        if 'entrada_manha' not in tipos_registrados:
            logger.info(f"[FALLBACK] ✅ Oferecendo entrada_manha (primeira batida faltante)")
            return 'entrada_manha'

        # 2. Se tem entrada_manha mas não tem saida_almoco, oferecer saida_almoco
        if 'entrada_manha' in tipos_registrados and 'saida_almoco' not in tipos_registrados:
            logger.info(f"[FALLBACK] ✅ Oferecendo saida_almoco (segunda batida faltante)")
            return 'saida_almoco'

        # 3. Se tem entrada_manha e saida_almoco mas não tem entrada_tarde, oferecer entrada_tarde
        if ('entrada_manha' in tipos_registrados and
            'saida_almoco' in tipos_registrados and
            'entrada_tarde' not in tipos_registrados):
            logger.info(f"[FALLBACK] ✅ Oferecendo entrada_tarde (terceira batida faltante)")
            return 'entrada_tarde'

        # 4. Se tem as três anteriores mas não tem saida, oferecer saida
        if ('entrada_manha' in tipos_registrados and
            'saida_almoco' in tipos_registrados and
            'entrada_tarde' in tipos_registrados and
            'saida' not in tipos_registrados):
            logger.info(f"[FALLBACK] ✅ Oferecendo saida (quarta batida faltante)")
            return 'saida'

        # 5. Se já tem todos os registros básicos, verificar extras (B5/B6)
        logger.info(f"[FALLBACK] Todos os registros básicos completos, verificando horas extras")

        # Verificar se pode ser início de hora extra (B5)
        if 'inicio_extra' not in tipos_registrados:
            logger.info(f"[FALLBACK] ✅ Oferecendo inicio_extra (B5 - quinta batida)")
            return 'inicio_extra'

        # Verificar se pode ser fim de hora extra (B6)
        if 'fim_extra' not in tipos_registrados:
            logger.info(f"[FALLBACK] ✅ Oferecendo fim_extra (B6 - sexta batida)")
            return 'fim_extra'

        # Se chegou até aqui, retornar None (dia completo)
        logger.warning(f"[FALLBACK] ❌ Funcionário {funcionario_id} já completou TODOS os registros do dia (B1-B6)")
        return None



    except Exception as e:
        logger.error(f"❌ ERRO na classificação inteligente para funcionário {funcionario_id}: {e}")
        logger.error(f"   Batida: #{numero_batida}, Hora: {hora_atual}")
        # Fallback para método original como último recurso
        logger.info(f"[ERRO] Usando método de sequência simples como fallback")
        return classificar_batida_por_sequencia(numero_batida, turno_info)

def inferir_batidas_ausentes(funcionario_id, data_referencia=None):
    """
    Infere batidas ausentes e reorganiza a sequência lógica.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado da inferência com alertas e sugestões
    """
    try:
        batidas = obter_batidas_do_dia(funcionario_id, data_referencia)
        turno_info = determinar_turno_funcionario(funcionario_id, data_referencia)

        resultado = {
            'batidas_originais': batidas,
            'batidas_inferidas': [],
            'alertas': [],
            'pendencias': [],
            'status': 'ok',
            'turno': turno_info
        }

        if not batidas:
            resultado['status'] = 'sem_batidas'
            resultado['alertas'].append('Nenhuma batida registrada no dia')
            return resultado

        # Analisar quantidade de batidas
        num_batidas = len(batidas)

        if num_batidas == 1:
            # Apenas uma batida - jornada incompleta
            resultado['status'] = 'incompleta'
            resultado['alertas'].append('Jornada incompleta - apenas entrada registrada')
            resultado['pendencias'].append({
                'tipo': 'jornada_incompleta',
                'descricao': 'Funcionário registrou apenas a entrada',
                'requer_acao': True
            })

        elif num_batidas == 2:
            # Duas batidas - pode ser entrada e saída sem intervalo
            primeira = batidas[0]
            segunda = batidas[1]

            # Calcular diferença de tempo
            diff_horas = (segunda['data_hora'] - primeira['data_hora']).total_seconds() / 3600

            if diff_horas > 6:
                # Jornada longa sem intervalo - problema legal
                resultado['alertas'].append('Jornada superior a 6 horas sem registro de intervalo')
                resultado['pendencias'].append({
                    'tipo': 'intervalo_obrigatorio',
                    'descricao': f'Jornada de {diff_horas:.1f}h sem intervalo registrado',
                    'requer_acao': True
                })

        elif num_batidas == 3:
            # Três batidas - falta uma (provavelmente saída final)
            resultado['alertas'].append('Batida de saída ausente')
            resultado['pendencias'].append({
                'tipo': 'saida_ausente',
                'descricao': 'Funcionário não registrou a saída final',
                'requer_acao': False  # Pode ser registrada posteriormente
            })

        elif num_batidas > 4:
            # Batidas extras - usar apenas as 4 primeiras
            resultado['alertas'].append(f'Batidas extras detectadas ({num_batidas} total)')
            resultado['batidas_extras'] = batidas[4:]
            batidas = batidas[:4]  # Usar apenas as 4 primeiras

        # Reclassificar batidas conforme sequência lógica
        for i, batida in enumerate(batidas):
            numero_sequencia = i + 1
            tipo_inferido = classificar_batida_por_sequencia(numero_sequencia, turno_info)

            batida_inferida = batida.copy()
            batida_inferida['tipo_original'] = batida['tipo_registro']
            batida_inferida['tipo_inferido'] = tipo_inferido
            batida_inferida['numero_sequencia'] = numero_sequencia

            # Verificar se tipo original difere do inferido
            if batida['tipo_registro'] != tipo_inferido:
                resultado['alertas'].append(
                    f"Batida {numero_sequencia}: {batida['tipo_registro']} → {tipo_inferido}"
                )

            resultado['batidas_inferidas'].append(batida_inferida)

        return resultado

    except Exception as e:
        logger.error(f"Erro ao inferir batidas ausentes: {e}")
        return {
            'batidas_originais': [],
            'batidas_inferidas': [],
            'alertas': [f'Erro na inferência: {str(e)}'],
            'pendencias': [],
            'status': 'erro',
            'turno': None
        }

# ========================================
# SISTEMA DE ALERTAS E PENDÊNCIAS
# ========================================

def criar_alerta_ponto(funcionario_id, data_referencia, tipo_alerta, descricao,
                      prioridade='media', requer_acao=False, dados_contexto=None):
    """
    Cria um alerta de ponto no banco de dados.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date): Data da jornada
        tipo_alerta (str): Tipo do alerta
        descricao (str): Descrição do alerta
        prioridade (str): Prioridade (baixa, media, alta, critica)
        requer_acao (bool): Se requer ação manual
        dados_contexto (dict): Dados adicionais em JSON

    Returns:
        int: ID do alerta criado ou None se erro
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Verificar se já existe alerta similar
        cursor.execute("""
            SELECT id FROM alertas_ponto
            WHERE funcionario_id = %s
            AND data_referencia = %s
            AND tipo_alerta = %s
            AND status_alerta = 'pendente'
        """, (funcionario_id, data_referencia, tipo_alerta))

        alerta_existente = cursor.fetchone()
        if alerta_existente:
            logger.info(f"Alerta {tipo_alerta} já existe para funcionário {funcionario_id} em {data_referencia}")
            cursor.close()
            conn.close()
            return alerta_existente['id']

        # Inserir novo alerta
        cursor.execute("""
            INSERT INTO alertas_ponto
            (funcionario_id, data_referencia, tipo_alerta, descricao,
             prioridade, requer_acao, dados_contexto)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            funcionario_id, data_referencia, tipo_alerta, descricao,
            prioridade, requer_acao,
            json.dumps(dados_contexto) if dados_contexto else None
        ))

        alerta_id = cursor.lastrowid
        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"Alerta criado: ID {alerta_id}, Tipo: {tipo_alerta}, Funcionário: {funcionario_id}")
        return alerta_id

    except Exception as e:
        logger.error(f"Erro ao criar alerta: {e}")
        return None

def processar_alertas_jornada(funcionario_id, data_referencia=None):
    """
    Processa e cria alertas baseados na análise da jornada do funcionário.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado do processamento com alertas criados
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        # Obter inferência das batidas
        resultado_inferencia = inferir_batidas_ausentes(funcionario_id, data_referencia)

        alertas_criados = []

        # Processar cada alerta da inferência
        for alerta in resultado_inferencia.get('alertas', []):
            # Determinar tipo de alerta baseado na mensagem
            tipo_alerta = 'jornada_incompleta'  # padrão
            prioridade = 'media'
            requer_acao = False

            if 'apenas entrada' in alerta.lower():
                tipo_alerta = 'jornada_incompleta'
                prioridade = 'alta'
                requer_acao = True
            elif 'saída ausente' in alerta.lower():
                tipo_alerta = 'saida_ausente'
                prioridade = 'media'
                requer_acao = False
            elif 'intervalo' in alerta.lower() and '6 horas' in alerta.lower():
                tipo_alerta = 'intervalo_obrigatorio'
                prioridade = 'critica'
                requer_acao = True
            elif 'batidas extras' in alerta.lower():
                tipo_alerta = 'batidas_extras'
                prioridade = 'baixa'
                requer_acao = False
            elif '→' in alerta:  # Batida reclassificada
                tipo_alerta = 'sequencia_invalida'
                prioridade = 'media'
                requer_acao = False

            # Criar alerta
            alerta_id = criar_alerta_ponto(
                funcionario_id=funcionario_id,
                data_referencia=data_referencia,
                tipo_alerta=tipo_alerta,
                descricao=alerta,
                prioridade=prioridade,
                requer_acao=requer_acao,
                dados_contexto={
                    'turno': resultado_inferencia.get('turno', {}).get('turno'),
                    'num_batidas': len(resultado_inferencia.get('batidas_originais', [])),
                    'status_inferencia': resultado_inferencia.get('status')
                }
            )

            if alerta_id:
                alertas_criados.append({
                    'id': alerta_id,
                    'tipo': tipo_alerta,
                    'descricao': alerta
                })

        # Processar pendências específicas
        for pendencia in resultado_inferencia.get('pendencias', []):
            tipo_alerta = pendencia.get('tipo', 'jornada_incompleta')

            alerta_id = criar_alerta_ponto(
                funcionario_id=funcionario_id,
                data_referencia=data_referencia,
                tipo_alerta=tipo_alerta,
                descricao=pendencia.get('descricao', ''),
                prioridade='alta' if pendencia.get('requer_acao') else 'media',
                requer_acao=pendencia.get('requer_acao', False),
                dados_contexto=pendencia
            )

            if alerta_id:
                alertas_criados.append({
                    'id': alerta_id,
                    'tipo': tipo_alerta,
                    'descricao': pendencia.get('descricao', '')
                })

        return {
            'success': True,
            'alertas_criados': alertas_criados,
            'total_alertas': len(alertas_criados),
            'inferencia': resultado_inferencia
        }

    except Exception as e:
        logger.error(f"Erro ao processar alertas da jornada: {e}")
        return {
            'success': False,
            'error': str(e),
            'alertas_criados': [],
            'total_alertas': 0
        }

def normalizar_caminho_foto(foto_3x4):
    """
    Normaliza o caminho da foto para garantir que sempre comece com /static/
    
    Args:
        foto_3x4 (str): Caminho da foto do banco de dados
        
    Returns:
        str or None: Caminho normalizado ou None se não houver foto válida
    """
    if not foto_3x4:
        return None
    
    # Ignorar o placeholder padrão
    if foto_3x4 == '/static/images/funcionario_sem_foto.svg':
        return None
    
    # Se já começa com /static/, retornar como está
    if foto_3x4.startswith('/static/'):
        return foto_3x4
    
    # Se não começa com /static/, adicionar o prefixo
    if foto_3x4.startswith('static/'):
        return '/' + foto_3x4
    else:
        return '/static/' + foto_3x4

def validar_duplicata_registro(funcionario_id, tipo_registro, data_atual):
    """
    Verifica se já existe um registro do mesmo tipo para o funcionário na data atual.
    Também verifica se a sequência lógica dos registros está sendo respeitada.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        data_atual (date): Data para verificação
    
    Returns:
        dict: Resultado com existência de duplicata e mensagem
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # ✅ MELHORIA: Verificar a data de cadastro do funcionário
        cursor.execute("""
            SELECT 
                DATE(data_cadastro) AS data_cadastro, 
                nome_completo
            FROM funcionarios
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        info_funcionario = cursor.fetchone()
        
        if not info_funcionario:
            return {
                'existe': True,
                'mensagem': f"Funcionário ID {funcionario_id} não encontrado ou está inativo",
                'registro_existente': None
            }
        
        # ✅ MELHORIA: Se a data atual for igual à data de cadastro, verificar se foi cadastrado à tarde
        # Nesse caso, ignorar entradas anteriores ao cadastro
        data_cadastro = info_funcionario['data_cadastro'].date() if hasattr(info_funcionario['data_cadastro'], 'date') else info_funcionario['data_cadastro']
        
        # Verificar duplicata no mesmo dia
        data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
        data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
        
        # ✅ CORREÇÃO: Buscar mais informações sobre o registro existente
        cursor.execute("""
            SELECT 
                id, 
                tipo_registro, 
                DATE_FORMAT(data_hora, '%H:%i:%s') as hora_registro,
                DATE_FORMAT(data_hora, '%d/%m/%Y %H:%i:%s') as data_hora_completa,
                metodo_registro,
                observacoes,
                IFNULL(status_pontualidade, 'Registrado') as status_pontualidade
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND data_hora BETWEEN %s AND %s
            ORDER BY data_hora ASC
        """, (funcionario_id, data_inicio, data_fim))
        
        registros_do_dia = cursor.fetchall()
        
        # Verificar duplicata do mesmo tipo
        for registro in registros_do_dia:
            if registro['tipo_registro'] == tipo_registro:
                # ✅ CORREÇÃO: Formatar tipo de registro para a mensagem
                tipo_formatado = tipo_registro.replace('_', ' ').title()
                
                # ✅ CORREÇÃO: Retornar informações mais detalhadas
                return {
                    'existe': True, 
                    'mensagem': f"Já existe um registro de {tipo_formatado} para hoje às {registro['hora_registro']} ({registro['status_pontualidade']})",
                    'registro_existente': {
                        'id': registro['id'],
                        'hora': registro['hora_registro'],
                        'data_hora_completa': registro['data_hora_completa'],
                        'status_pontualidade': registro['status_pontualidade'],
                        'metodo_registro': registro['metodo_registro'],
                        'observacoes': registro['observacoes'] if registro['observacoes'] else None
                    }
                }
        
        # ✅ MELHORIA 1: Considerar jornada a partir do dia seguinte ao cadastro
        if data_cadastro == data_atual:
            # Se estamos no mesmo dia do cadastro, não exigir registros anteriores
            # Isso evita marcar como ausência pontos anteriores ao cadastro
            logger.info(f"Funcionário {info_funcionario['nome_completo']} cadastrado hoje. Ignorando verificação de sequência para primeiro dia.")
            
            # Nesse caso, retornamos sem verificação de sequência
            conn.close()
            return {'existe': False, 'mensagem': '', 'registro_existente': None}
        
        # ✅ MELHORIA 2: Tornar a verificação de sequência lógica opcional
        # A verificação de sequência agora só acontece se o parâmetro VERIFICAR_SEQUENCIA for True
        # Vamos criar uma variável no escopo global para controlar isso
        VERIFICAR_SEQUENCIA = False  # Definido como False para remover dependência entre registros
        
        if VERIFICAR_SEQUENCIA:
            # A ordem correta é: entrada_manha -> saida_almoco -> entrada_tarde -> saida
            sequencia_correta = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
            
            # Verificar qual é a posição do tipo de registro na sequência
            if tipo_registro in sequencia_correta:
                indice_atual = sequencia_correta.index(tipo_registro)
                
                # Se não for o primeiro registro da sequência, verificar se o anterior foi registrado
                if indice_atual > 0:
                    tipo_anterior = sequencia_correta[indice_atual - 1]
                    tipo_anterior_registrado = False
                    
                    for registro in registros_do_dia:
                        if registro['tipo_registro'] == tipo_anterior:
                            tipo_anterior_registrado = True
                            break
                    
                    if not tipo_anterior_registrado:
                        tipo_anterior_formatado = tipo_anterior.replace('_', ' ').title()
                        conn.close()
                        return {
                            'existe': True,
                            'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} sem antes registrar {tipo_anterior_formatado}",
                            'registro_existente': None
                        }
                
                # Verificar se há registros posteriores na sequência que já foram feitos
                if indice_atual < len(sequencia_correta) - 1:
                    for i in range(indice_atual + 1, len(sequencia_correta)):
                        tipo_posterior = sequencia_correta[i]
                        
                        for registro in registros_do_dia:
                            if registro['tipo_registro'] == tipo_posterior:
                                tipo_posterior_formatado = tipo_posterior.replace('_', ' ').title()
                                conn.close()
                                return {
                                    'existe': True,
                                    'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} pois já existe um registro posterior ({tipo_posterior_formatado}) hoje",
                                    'registro_existente': None
                                }
        
        conn.close()
        # Nenhuma duplicata ou problema de sequência encontrado
        return {'existe': False, 'mensagem': '', 'registro_existente': None}
        
    except Exception as e:
        logger.error(f"Erro ao validar duplicata de registro: {str(e)}")
        return {'existe': False, 'mensagem': '', 'registro_existente': None}



def registrar_ponto_no_banco(funcionario_id, tipo_registro, metodo_registro, observacoes=None, qualidade_biometria=None, status_pontualidade=None, hora_customizada=None):
    """
    Registra um ponto no banco de dados.

    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        metodo_registro (str): Método de registro (biometrico, manual)
        observacoes (str, optional): Observações adicionais
        qualidade_biometria (int, optional): Qualidade da biometria (0-100)
        status_pontualidade (str, optional): Status da pontualidade (Pontual, Atrasado)
        hora_customizada (datetime, optional): Horário customizado para o registro (usado para ajustes automáticos)

    Returns:
        dict: Resultado da operação
    """
    try:
        # Garantir que a tabela existe (SIMPLIFICADO)
        # verificar_e_criar_tabela_registros()  # Desabilitado temporariamente
        
        # Obter conexão com o banco
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter usuário atual (para registro manual)
        usuario_id = None
        if metodo_registro == 'manual' and 'usuario_id' in session:
            usuario_id = session.get('usuario_id')
        
        # Obter endereço IP e user agent
        ip_origem = request.remote_addr if request else None
        user_agent = request.headers.get('User-Agent') if request else None
        
        # ✅ CORREÇÃO: Garantir que status_pontualidade seja 'Pontual' ou 'Atrasado'
        if status_pontualidade not in ['Pontual', 'Atrasado']:
            status_pontualidade = 'Pontual'  # Valor padrão

        # ✅ CORREÇÃO: Validar observações para evitar problemas no banco
        if observacoes and len(observacoes) > 1000:  # Limitar tamanho para evitar problemas
            observacoes = observacoes[:1000]
        
        # ✅ NOVA LÓGICA: Aplicar validações das novas regras se disponíveis (TEMPORARIAMENTE DESABILITADO)
        if False and NOVAS_REGRAS_ATIVAS and not status_pontualidade:
            try:
                hora_atual = datetime.now()

                # Validar entrada da manhã com novas regras
                if tipo_registro == 'entrada_manha':
                    validacao = validar_entrada_manha_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'atrasado':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = f"Atraso de {validacao['minutos_atraso']} minutos"
                    elif validacao['status'] == 'pontual':
                        status_pontualidade = 'Pontual'
                    elif validacao['status'] == 'ausente_manha':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = "Registro após período da manhã"

                # Validar saída com novas regras
                elif tipo_registro == 'saida':
                    validacao = validar_saida_expediente_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'saida_antecipada':
                        status_pontualidade = 'Atrasado'  # Saída antecipada como irregularidade
                        if not observacoes:
                            observacoes = f"Saída antecipada em {validacao['minutos_antecipacao']} minutos"
                    else:
                        status_pontualidade = 'Pontual'

                # Para outros tipos, manter lógica padrão
                else:
                    status_pontualidade = status_pontualidade or 'Pontual'

            except Exception as e:
                logger.warning(f"Erro ao aplicar novas regras: {e}")
                status_pontualidade = status_pontualidade or 'Pontual'
        else:
            status_pontualidade = status_pontualidade or 'Pontual'

        # ✅ NOVA FUNCIONALIDADE: Inserir registro com horário customizado se fornecido
        if hora_customizada:
            # Usar horário customizado (para ajustes automáticos de intervalo)
            cursor.execute("""
                INSERT INTO registros_ponto
                (funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por,
                 template_biometrico, qualidade_biometria, observacoes, ip_origem, user_agent,
                 status_pontualidade)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                funcionario_id,
                tipo_registro,
                hora_customizada,
                metodo_registro,
                usuario_id,
                None,  # template_biometrico
                qualidade_biometria,
                observacoes,
                ip_origem,
                user_agent,
                status_pontualidade
            ))
        else:
            # Usar horário atual
            cursor.execute("""
                INSERT INTO registros_ponto
                (funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por,
                 template_biometrico, qualidade_biometria, observacoes, ip_origem, user_agent,
                 status_pontualidade)
                VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                funcionario_id,
                tipo_registro,
                metodo_registro,
                usuario_id,
                None,  # template_biometrico
                qualidade_biometria,
                observacoes,
                ip_origem,
                user_agent,
                status_pontualidade
            ))
        
        registro_id = cursor.lastrowid
        conn.commit()

        # ✅ LOGS ESPECÍFICOS: Log detalhado do registro
        if hora_customizada:
            logger.info(f"[REGISTRO AJUSTADO] Funcionário: {funcionario_id}, Tipo: {tipo_registro}, Método: {metodo_registro}, Status: {status_pontualidade}, Horário Ajustado: {hora_customizada.strftime('%H:%M:%S')}")
        else:
            logger.info(f"[REGISTRO] Funcionário: {funcionario_id}, Tipo: {tipo_registro}, Método: {metodo_registro}, Status: {status_pontualidade}")

        conn.close()

        # ✅ NOVA FUNCIONALIDADE: Processar banco de horas automaticamente (TEMPORARIAMENTE DESABILITADO)
        if False and NOVAS_REGRAS_ATIVAS:
            try:
                data_atual = date.today()
                sucesso_banco = processar_banco_horas_dia(funcionario_id, data_atual)
                if sucesso_banco:
                    logger.info(f"✅ Banco de horas processado para funcionário {funcionario_id} em {data_atual}")
                else:
                    logger.warning(f"⚠️ Falha ao processar banco de horas para funcionário {funcionario_id}")
            except Exception as e:
                logger.error(f"❌ Erro ao processar banco de horas: {e}")

        # ✅ NOVA LÓGICA: Processar alertas e inferências após registro (TEMPORARIAMENTE DESABILITADO)
        try:
            # Desabilitado temporariamente para isolar problemas
            pass
        except Exception as e:
            logger.error(f"❌ Erro ao processar alertas: {e}")

        return {
            'success': True,
            'registro_id': registro_id,
            'message': 'Registro salvo com sucesso'
        }

    except Exception as e:
        logger.error(f"Erro ao registrar ponto no banco: {str(e)}")

        # ✅ TRATAMENTO ELEGANTE: Erro de chave duplicada
        if "Duplicate entry" in str(e) and "uk_funcionario_tipo_data" in str(e):
            # Extrair tipo de registro do erro
            erro_str = str(e)
            if "saida_almoco" in erro_str:
                tipo_formatado = "Saída para Intervalo"
            elif "entrada_manha" in erro_str:
                tipo_formatado = "Entrada da Manhã"
            elif "entrada_tarde" in erro_str:
                tipo_formatado = "Retorno do Intervalo"
            elif "saida" in erro_str:
                tipo_formatado = "Saída Final"
            else:
                tipo_formatado = "este tipo de registro"

            return {
                'success': False,
                'message': f'Você já registrou {tipo_formatado} hoje. Cada tipo de ponto pode ser registrado apenas uma vez por dia.',
                'code': 'DUPLICATE_RECORD'
            }

        # ✅ TRATAMENTO ELEGANTE: Outros erros de banco
        elif "Connection" in str(e) or "timeout" in str(e).lower():
            return {
                'success': False,
                'message': 'Falha na conexão com o sistema. Tente novamente em alguns instantes.',
                'code': 'CONNECTION_ERROR'
            }

        # ✅ TRATAMENTO ELEGANTE: Erro genérico
        else:
            return {
                'success': False,
                'message': 'Ocorreu um erro interno. Nossa equipe foi notificada e está trabalhando na solução.',
                'code': 'INTERNAL_ERROR'
            }

def registrar_ponto_inteligente(funcionario_id, metodo_registro='manual', observacoes=None,
                               qualidade_biometria=None, forcar_tipo=None):
    """
    Registra ponto com determinação automática do tipo baseado na nova lógica.

    Args:
        funcionario_id (int): ID do funcionário
        metodo_registro (str): Método de registro (biometrico, manual)
        observacoes (str, optional): Observações adicionais
        qualidade_biometria (int, optional): Qualidade da biometria (0-100)
        forcar_tipo (str, optional): Forçar tipo específico de batida

    Returns:
        dict: Resultado da operação com informações detalhadas
    """
    try:
        data_atual = date.today()

        # Obter batidas existentes do dia
        batidas_existentes = obter_batidas_do_dia(funcionario_id, data_atual)

        # Determinar turno baseado na primeira batida ou horário atual
        turno_info = determinar_turno_funcionario(funcionario_id, data_atual)

        # Determinar tipo de batida automaticamente
        if forcar_tipo:
            tipo_registro = forcar_tipo
            logger.info(f"Tipo de batida forçado: {tipo_registro}")
        else:
            num_batidas = len(batidas_existentes)
            # ✅ CORREÇÃO: Usar classificação inteligente que considera horário atual
            tipo_registro = classificar_batida_inteligente(funcionario_id, num_batidas + 1, turno_info, datetime.now())

            # ✅ VALIDAÇÃO: Verificar se a classificação foi bem-sucedida
            if tipo_registro is None:
                return {
                    'success': False,
                    'message': 'Funcionário não possui horário de trabalho configurado. Entre em contato com o administrador.',
                    'tipo_sugerido': None,
                    'turno_determinado': turno_info
                }

            logger.info(f"Tipo de batida determinado automaticamente (INTELIGENTE): {tipo_registro} (batida #{num_batidas + 1})")

        # Validar se não há batidas duplicadas no mesmo minuto
        agora = datetime.now()
        for batida in batidas_existentes:
            diff_minutos = abs((agora - batida['data_hora']).total_seconds() / 60)
            if diff_minutos < 1:  # Menos de 1 minuto de diferença
                return {
                    'success': False,
                    'message': 'Batida muito próxima da anterior. Aguarde pelo menos 1 minuto.',
                    'tipo_sugerido': tipo_registro,
                    'turno_determinado': turno_info
                }

        # ✅ CORREÇÃO V2: Permitir B5 e B6 (até 6 batidas)
        if num_batidas >= 6:
            return {
                'success': False,
                'message': 'Limite de 6 batidas por dia atingido (B1-B6). Verifique os registros existentes.',
                'batidas_existentes': len(batidas_existentes),
                'turno_determinado': turno_info
            }

        # Determinar status de pontualidade baseado no turno e horário
        status_pontualidade = 'Pontual'  # Padrão - pode ser melhorado com regras específicas

        # Registrar o ponto
        resultado_registro = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro=metodo_registro,
            observacoes=observacoes,
            qualidade_biometria=qualidade_biometria,
            status_pontualidade=status_pontualidade
        )

        if resultado_registro['success']:
            # Adicionar informações extras ao resultado
            resultado_registro.update({
                'tipo_determinado': tipo_registro,
                'turno_determinado': turno_info,
                'numero_batida': num_batidas + 1,
                'batidas_anteriores': num_batidas,
                'metodo_determinacao': 'automatico' if not forcar_tipo else 'forcado'
            })

            logger.info(f"✅ Ponto registrado com sucesso - Funcionário: {funcionario_id}, "
                       f"Tipo: {tipo_registro}, Turno: {turno_info['turno']}, Batida: #{num_batidas + 1}")

        return resultado_registro

    except Exception as e:
        logger.error(f"Erro no registro inteligente de ponto: {e}")
        return {
            'success': False,
            'message': f'Erro no registro inteligente: {str(e)}'
        }

# ========================================
# VALIDAÇÃO DE SEQUÊNCIA LÓGICA
# ========================================

def validar_sequencia_batidas(funcionario_id, data_referencia=None):
    """
    Valida a sequência lógica das batidas do funcionário.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado da validação com problemas encontrados
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        batidas = obter_batidas_do_dia(funcionario_id, data_referencia)

        resultado = {
            'valida': True,
            'problemas': [],
            'alertas': [],
            'batidas_analisadas': len(batidas),
            'sequencia_esperada': [],
            'sequencia_real': []
        }

        if not batidas:
            resultado['valida'] = False
            resultado['problemas'].append('Nenhuma batida registrada')
            return resultado

        # Definir sequência esperada baseada no número de batidas
        sequencias_esperadas = {
            1: ['entrada_manha'],
            2: ['entrada_manha', 'saida'],
            3: ['entrada_manha', 'saida_almoco', 'entrada_tarde'],
            4: ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
        }

        num_batidas = len(batidas)
        sequencia_esperada = sequencias_esperadas.get(num_batidas, [])
        resultado['sequencia_esperada'] = sequencia_esperada

        # Analisar sequência real
        sequencia_real = [batida['tipo_registro'] for batida in batidas]
        resultado['sequencia_real'] = sequencia_real

        # Validar ordem cronológica
        for i in range(1, len(batidas)):
            batida_anterior = batidas[i-1]
            batida_atual = batidas[i]

            if batida_atual['data_hora'] <= batida_anterior['data_hora']:
                resultado['valida'] = False
                resultado['problemas'].append(
                    f"Batida {i+1} ({batida_atual['tipo_registro']}) registrada antes da batida {i} ({batida_anterior['tipo_registro']})"
                )

        # Validar tipos de batida em sequência
        if num_batidas <= 4 and sequencia_esperada:
            for i, (esperado, real) in enumerate(zip(sequencia_esperada, sequencia_real)):
                if esperado != real:
                    resultado['alertas'].append(
                        f"Batida {i+1}: esperado '{esperado}', registrado '{real}'"
                    )

        # Validar regras específicas
        if num_batidas >= 2:
            # Primeira batida deve ser entrada
            if not sequencia_real[0].startswith('entrada'):
                resultado['valida'] = False
                resultado['problemas'].append("Primeira batida deve ser uma entrada")

            # Última batida deve ser saída (se 2 ou 4 batidas)
            if num_batidas in [2, 4] and sequencia_real[-1] != 'saida':
                resultado['alertas'].append("Última batida deveria ser 'saida'")

        # Validar intervalo (se 3 ou 4 batidas)
        if num_batidas >= 3:
            if 'saida_almoco' not in sequencia_real:
                resultado['alertas'].append("Saída para almoço não registrada")
            if 'entrada_tarde' not in sequencia_real:
                resultado['alertas'].append("Retorno do almoço não registrado")

        # Validar intervalos de tempo
        if num_batidas >= 3:
            # Encontrar saída e retorno do almoço
            idx_saida_almoco = None
            idx_entrada_tarde = None

            for i, batida in enumerate(batidas):
                if batida['tipo_registro'] == 'saida_almoco':
                    idx_saida_almoco = i
                elif batida['tipo_registro'] == 'entrada_tarde':
                    idx_entrada_tarde = i

            if idx_saida_almoco is not None and idx_entrada_tarde is not None:
                if idx_entrada_tarde > idx_saida_almoco:
                    saida_almoco = batidas[idx_saida_almoco]['data_hora']
                    entrada_tarde = batidas[idx_entrada_tarde]['data_hora']
                    intervalo_minutos = (entrada_tarde - saida_almoco).total_seconds() / 60

                    if intervalo_minutos < 30:
                        resultado['alertas'].append(f"Intervalo muito curto: {intervalo_minutos:.0f} minutos")
                    elif intervalo_minutos > 120:
                        resultado['alertas'].append(f"Intervalo muito longo: {intervalo_minutos:.0f} minutos")

        # Validar jornada total
        if num_batidas >= 2:
            primeira_batida = batidas[0]['data_hora']
            ultima_batida = batidas[-1]['data_hora']
            jornada_horas = (ultima_batida - primeira_batida).total_seconds() / 3600

            if jornada_horas > 12:
                resultado['alertas'].append(f"Jornada muito longa: {jornada_horas:.1f} horas")
            elif jornada_horas < 4 and num_batidas >= 2:
                resultado['alertas'].append(f"Jornada muito curta: {jornada_horas:.1f} horas")

        return resultado

    except Exception as e:
        logger.error(f"Erro ao validar sequência de batidas: {e}")
        return {
            'valida': False,
            'problemas': [f'Erro na validação: {str(e)}'],
            'alertas': [],
            'batidas_analisadas': 0,
            'sequencia_esperada': [],
            'sequencia_real': []
        }

def corrigir_sequencia_automatica(funcionario_id, data_referencia=None):
    """
    Tenta corrigir automaticamente problemas na sequência de batidas.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado da correção automática
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        validacao = validar_sequencia_batidas(funcionario_id, data_referencia)

        if validacao['valida'] and not validacao['alertas']:
            return {
                'success': True,
                'message': 'Sequência já está correta',
                'correcoes_aplicadas': []
            }

        batidas = obter_batidas_do_dia(funcionario_id, data_referencia)
        correcoes = []

        # Tentar reclassificar batidas baseado na ordem cronológica
        if len(batidas) <= 4:
            turno_info = determinar_turno_funcionario(funcionario_id, data_referencia)

            for i, batida in enumerate(batidas):
                tipo_esperado = classificar_batida_por_sequencia(i + 1, turno_info)

                if batida['tipo_registro'] != tipo_esperado:
                    # Atualizar tipo de registro no banco
                    conn = get_db_connection()
                    cursor = conn.cursor()

                    cursor.execute("""
                        UPDATE registros_ponto
                        SET tipo_registro = %s,
                            observacoes = CONCAT(COALESCE(observacoes, ''), ' [Corrigido automaticamente]')
                        WHERE id = %s
                    """, (tipo_esperado, batida['id']))

                    conn.commit()
                    cursor.close()
                    conn.close()

                    correcoes.append({
                        'batida_id': batida['id'],
                        'tipo_anterior': batida['tipo_registro'],
                        'tipo_corrigido': tipo_esperado,
                        'horario': batida['data_hora'].strftime('%H:%M:%S')
                    })

                    logger.info(f"Batida corrigida: ID {batida['id']}, {batida['tipo_registro']} → {tipo_esperado}")

        return {
            'success': True,
            'message': f'{len(correcoes)} correções aplicadas',
            'correcoes_aplicadas': correcoes,
            'validacao_original': validacao
        }

    except Exception as e:
        logger.error(f"Erro ao corrigir sequência automaticamente: {e}")
        return {
            'success': False,
            'message': f'Erro na correção: {str(e)}',
            'correcoes_aplicadas': []
        }

# ========================================
# TRATAMENTO DE BATIDAS EXTRAS
# ========================================

def processar_batidas_extras(funcionario_id, data_referencia=None):
    """
    Processa casos de mais de 4 batidas por dia, selecionando as 4 mais relevantes.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado do processamento com batidas selecionadas e extras
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        batidas = obter_batidas_do_dia(funcionario_id, data_referencia)

        resultado = {
            'total_batidas': len(batidas),
            'batidas_validas': [],
            'batidas_extras': [],
            'criterio_selecao': 'cronologico',
            'alertas': []
        }

        if len(batidas) <= 4:
            resultado['batidas_validas'] = batidas
            resultado['alertas'].append('Número de batidas dentro do limite (≤4)')
            return resultado

        # Mais de 4 batidas - aplicar lógica de seleção
        resultado['alertas'].append(f'Detectadas {len(batidas)} batidas. Selecionando as 4 mais relevantes.')

        # Estratégia 1: Selecionar por padrão cronológico
        batidas_ordenadas = sorted(batidas, key=lambda x: x['data_hora'])

        # Tentar identificar padrão de jornada normal
        batidas_selecionadas = []
        batidas_extras = []

        # Primeira batida (entrada)
        primeira_entrada = None
        for batida in batidas_ordenadas:
            if batida['tipo_registro'].startswith('entrada'):
                primeira_entrada = batida
                break

        if primeira_entrada:
            batidas_selecionadas.append(primeira_entrada)

            # Procurar saída para almoço
            saida_almoco = None
            for batida in batidas_ordenadas:
                if (batida['tipo_registro'] == 'saida_almoco' and
                    batida['data_hora'] > primeira_entrada['data_hora']):
                    saida_almoco = batida
                    break

            if saida_almoco:
                batidas_selecionadas.append(saida_almoco)

                # Procurar retorno do almoço
                entrada_tarde = None
                for batida in batidas_ordenadas:
                    if (batida['tipo_registro'] == 'entrada_tarde' and
                        batida['data_hora'] > saida_almoco['data_hora']):
                        entrada_tarde = batida
                        break

                if entrada_tarde:
                    batidas_selecionadas.append(entrada_tarde)

                    # Procurar saída final
                    saida_final = None
                    for batida in batidas_ordenadas:
                        if (batida['tipo_registro'] == 'saida' and
                            batida['data_hora'] > entrada_tarde['data_hora']):
                            saida_final = batida
                            break

                    if saida_final:
                        batidas_selecionadas.append(saida_final)

        # Se não conseguiu identificar padrão completo, usar as 4 primeiras cronologicamente
        if len(batidas_selecionadas) < 4:
            batidas_selecionadas = batidas_ordenadas[:4]
            resultado['criterio_selecao'] = 'primeiras_4_cronologicas'
            resultado['alertas'].append('Padrão de jornada não identificado. Usando as 4 primeiras batidas.')

        # Identificar batidas extras
        ids_selecionadas = {batida['id'] for batida in batidas_selecionadas}
        batidas_extras = [batida for batida in batidas if batida['id'] not in ids_selecionadas]

        resultado['batidas_validas'] = batidas_selecionadas
        resultado['batidas_extras'] = batidas_extras

        # Marcar batidas extras no banco de dados
        if batidas_extras:
            conn = get_db_connection()
            cursor = conn.cursor()

            for batida_extra in batidas_extras:
                cursor.execute("""
                    UPDATE registros_ponto
                    SET observacoes = CONCAT(
                        COALESCE(observacoes, ''),
                        ' [BATIDA EXTRA - NÃO UTILIZADA NO CÁLCULO]'
                    )
                    WHERE id = %s
                """, (batida_extra['id'],))

            conn.commit()
            cursor.close()
            conn.close()

            resultado['alertas'].append(f'{len(batidas_extras)} batidas marcadas como extras')

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar batidas extras: {e}")
        return {
            'total_batidas': 0,
            'batidas_validas': [],
            'batidas_extras': [],
            'criterio_selecao': 'erro',
            'alertas': [f'Erro no processamento: {str(e)}']
        }

def validar_intervalo_obrigatorio(funcionario_id, data_referencia=None):
    """
    Valida se o intervalo obrigatório foi respeitado em jornadas longas.

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date, optional): Data de referência. Defaults to hoje.

    Returns:
        dict: Resultado da validação do intervalo
    """
    try:
        if data_referencia is None:
            data_referencia = date.today()

        # Processar batidas extras primeiro
        resultado_extras = processar_batidas_extras(funcionario_id, data_referencia)
        batidas_validas = resultado_extras['batidas_validas']

        resultado = {
            'intervalo_obrigatorio': False,
            'intervalo_registrado': False,
            'duracao_intervalo_minutos': 0,
            'jornada_horas': 0,
            'conforme': True,
            'alertas': [],
            'batidas_analisadas': len(batidas_validas)
        }

        if len(batidas_validas) < 2:
            resultado['alertas'].append('Jornada incompleta - não é possível validar intervalo')
            return resultado

        # Calcular duração da jornada
        primeira_batida = min(batidas_validas, key=lambda x: x['data_hora'])
        ultima_batida = max(batidas_validas, key=lambda x: x['data_hora'])
        jornada_segundos = (ultima_batida['data_hora'] - primeira_batida['data_hora']).total_seconds()
        jornada_horas = jornada_segundos / 3600

        resultado['jornada_horas'] = jornada_horas

        # Verificar se intervalo é obrigatório (jornadas > 6 horas)
        if jornada_horas > 6:
            resultado['intervalo_obrigatorio'] = True
            resultado['alertas'].append(f'Jornada de {jornada_horas:.1f}h requer intervalo obrigatório')

            # Verificar se há registro de intervalo
            saida_almoco = None
            entrada_tarde = None

            for batida in batidas_validas:
                if batida['tipo_registro'] == 'saida_almoco':
                    saida_almoco = batida
                elif batida['tipo_registro'] == 'entrada_tarde':
                    entrada_tarde = batida

            if saida_almoco and entrada_tarde:
                resultado['intervalo_registrado'] = True
                intervalo_segundos = (entrada_tarde['data_hora'] - saida_almoco['data_hora']).total_seconds()
                resultado['duracao_intervalo_minutos'] = intervalo_segundos / 60

                # Validar duração mínima do intervalo (1 hora = 60 minutos)
                if resultado['duracao_intervalo_minutos'] < 60:
                    resultado['conforme'] = False
                    resultado['alertas'].append(
                        f'Intervalo insuficiente: {resultado["duracao_intervalo_minutos"]:.0f} min (mínimo: 60 min)'
                    )
                else:
                    resultado['alertas'].append(
                        f'Intervalo conforme: {resultado["duracao_intervalo_minutos"]:.0f} minutos'
                    )
            else:
                resultado['conforme'] = False
                resultado['alertas'].append('Intervalo obrigatório não registrado')
        else:
            resultado['alertas'].append(f'Jornada de {jornada_horas:.1f}h não requer intervalo obrigatório')

        return resultado

    except Exception as e:
        logger.error(f"Erro ao validar intervalo obrigatório: {e}")
        return {
            'intervalo_obrigatorio': False,
            'intervalo_registrado': False,
            'duracao_intervalo_minutos': 0,
            'jornada_horas': 0,
            'conforme': False,
            'alertas': [f'Erro na validação: {str(e)}'],
            'batidas_analisadas': 0
        }

def verificar_e_criar_tabela_registros():
    """
    Verifica se a tabela registros_ponto existe e cria se necessário.
    Também verifica se a coluna status_pontualidade existe e adiciona se necessário.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Verificar se a tabela existe
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'registros_ponto'
        """)
        
        result = cursor.fetchone()
        existe = result['COUNT(*)'] > 0
        
        if not existe:
            logger.warning("[CORREÇÃO] Tabela registros_ponto não existe. Criando...")
            
            # Criar a tabela com a coluna status_pontualidade
            cursor.execute("""
                CREATE TABLE registros_ponto (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    funcionario_id INT NOT NULL,
                    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
                    metodo_registro ENUM('biometrico', 'manual') NOT NULL,
                    data_hora DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    qualidade_biometria INT NULL,
                    observacoes TEXT NULL,
                    ip_origem VARCHAR(45) NULL,
                    criado_por INT NULL,
                    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status_pontualidade ENUM('Pontual', 'Atrasado', 'Saída Antecipada') NULL COMMENT 'Status de pontualidade do registro',
                    INDEX idx_funcionario_data (funcionario_id, data_hora),
                    INDEX idx_tipo_registro (tipo_registro),
                    INDEX idx_metodo_registro (metodo_registro),
                    INDEX idx_status_pontualidade (status_pontualidade),
                    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
                    FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            conn.commit()
            logger.info("[CORREÇÃO] Tabela registros_ponto criada com sucesso!")
        else:
            # Verificar se a coluna status_pontualidade existe
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = 'controle_ponto' 
                AND table_name = 'registros_ponto' 
                AND column_name = 'status_pontualidade'
            """)
            
            result = cursor.fetchone()
            coluna_existe = result['COUNT(*)'] > 0
            
            if not coluna_existe:
                logger.warning("[CORREÇÃO] Adicionando coluna status_pontualidade à tabela registros_ponto...")
                
                # Adicionar a coluna status_pontualidade
                cursor.execute("""
                    ALTER TABLE registros_ponto
                    ADD COLUMN status_pontualidade ENUM('Pontual', 'Atrasado', 'Saída Antecipada') NULL
                    COMMENT 'Status de pontualidade do registro'
                """)

                # Adicionar índice para a nova coluna
                cursor.execute("""
                    ALTER TABLE registros_ponto
                    ADD INDEX idx_status_pontualidade (status_pontualidade)
                """)

                conn.commit()
                logger.info("[CORREÇÃO] Coluna status_pontualidade adicionada com sucesso!")
            else:
                # ✅ ATUALIZAR ENUM para incluir 'Saída Antecipada' se a coluna já existe
                try:
                    cursor.execute("""
                        ALTER TABLE registros_ponto
                        MODIFY COLUMN status_pontualidade
                        ENUM('Pontual', 'Atrasado', 'Saída Antecipada') NULL
                        COMMENT 'Status de pontualidade do registro'
                    """)
                    conn.commit()
                    logger.info("[CORREÇÃO] ENUM status_pontualidade atualizado com 'Saída Antecipada'")
                except Exception as enum_error:
                    logger.warning(f"Erro ao atualizar ENUM (pode já estar atualizado): {enum_error}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"[CORREÇÃO] Erro ao verificar/criar tabela registros_ponto: {str(e)}")
        return False

def obter_hora_atual(fuso_horario_manaus=True):
    """
    Obtém a hora atual do sistema, com preferência por NTP quando disponível.
    Se solicitado, ajusta para o fuso horário de Manaus/AM (GMT-4).

    Returns:
        datetime: Data e hora atual, ajustada para fuso de Manaus se solicitado
    """
    hora_atual = None
    
    # Tentar obter hora via NTP
    try:
        client = ntplib.NTPClient()
        response = client.request('pool.ntp.org', timeout=1)
        hora_atual = datetime.fromtimestamp(response.tx_time)
        logger.info("Hora obtida via NTP")
    except Exception as e:
        logger.warning(f"Erro ao obter hora via NTP: {str(e)}. Usando hora local.")
        hora_atual = datetime.now()
    
    # Ajustar para fuso de Manaus (GMT-4) se solicitado
    if fuso_horario_manaus:
        # A implementação ideal usaria pytz ou datetime.astimezone()
        # Esta é uma aproximação simplificada para o fuso de Manaus (-4h)
        # Em produção com Python 3.9+, use zoneinfo ou pytz
        
        # Verificamos se a hora já está no fuso correto
        # Se a diferença for maior que 4 horas, ajustamos
        # Essa lógica assume que o servidor está em GMT
        hora_utc = datetime.utcnow()
        diferenca = (hora_atual - hora_utc).total_seconds() / 3600
        
        # Se não estiver próximo do fuso de Manaus (-4h)
        if abs(diferenca + 4) > 1:
            logger.info(f"Ajustando fuso: diferença atual é {diferenca}h do UTC")
            # Ajustar para GMT-4 (Manaus)
            hora_atual = hora_utc.replace(tzinfo=None) - datetime.timedelta(hours=4)
    
    return hora_atual

def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario, funcionario_id=None):
    """
    Valida se o tipo de registro solicitado é permitido no horário atual.

    Args:
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        horarios_funcionario (dict): Horários configurados do funcionário
        funcionario_id (int, optional): ID do funcionário (necessário para validações de intervalo)

    Returns:
        dict: {'permitido': bool, 'mensagem': str, 'horario_liberado': bool, 'status': str}
    """
    # Obter hora atual no fuso de Manaus
    hora_atual = obter_hora_atual(fuso_horario_manaus=True)
    hora_atual_str = hora_atual.strftime('%H:%M')
    
    # Converter horários de string para objetos time para comparação
    def string_para_time(hora_str):
        if not hora_str:
            return None
        try:
            # Tratar formato HH:MM:SS ou HH:MM
            partes = hora_str.split(':')
            if len(partes) >= 2:
                horas, minutos = int(partes[0]), int(partes[1])
                return time(horas, minutos)
            return None
        except (ValueError, AttributeError):
            return None
    
    entrada_manha = string_para_time(horarios_funcionario['entrada_manha'])
    saida_almoco = string_para_time(horarios_funcionario['saida_almoco'])
    entrada_tarde = string_para_time(horarios_funcionario['entrada_tarde'])
    saida = string_para_time(horarios_funcionario['saida'])
    
    # Tolerância em minutos
    tolerancia = horarios_funcionario['tolerancia_minutos']
    
    # Converter hora atual para objeto time (se necessário)
    if isinstance(hora_atual, datetime):
        hora_atual_obj = hora_atual.time()
    else:
        hora_atual_obj = hora_atual  # Já é um objeto time
    
    # ✅ CORREÇÃO CRÍTICA: Determinar o período do dia CORRETAMENTE
    # O sistema deve considerar os períodos de forma mais flexível para funcionários atrasados
    periodo_atual = "indefinido"

    # ✅ LÓGICA CORRIGIDA: Verificar períodos em ordem cronológica

    # 1. ANTES DO EXPEDIENTE (antes da entrada da manhã + tolerância)
    if entrada_manha:
        limite_entrada = datetime.combine(date.today(), entrada_manha)
        limite_entrada = (limite_entrada + timedelta(minutes=tolerancia)).time()

        if hora_atual_obj < limite_entrada:
            periodo_atual = "antes_expediente"

    # 2. PERÍODO DA MANHÃ (desde entrada até saída para almoço)
    if entrada_manha and saida_almoco and periodo_atual == "indefinido":
        # ✅ CORREÇÃO: Permitir período manhã mesmo para atrasados
        # Manhã vai desde a entrada programada até a saída para almoço
        if hora_atual_obj < saida_almoco:
            periodo_atual = "manha"

    # 3. PERÍODO DE ALMOÇO (entre saída e retorno do almoço)
    if saida_almoco and entrada_tarde and periodo_atual == "indefinido":
        if saida_almoco <= hora_atual_obj < entrada_tarde:
            periodo_atual = "almoco"

    # 4. PERÍODO DA TARDE (desde retorno do almoço até saída)
    if entrada_tarde and saida and periodo_atual == "indefinido":
        if entrada_tarde <= hora_atual_obj <= saida:
            periodo_atual = "tarde"

    # 5. APÓS EXPEDIENTE (depois da saída)
    if saida and periodo_atual == "indefinido":
        if hora_atual_obj > saida:
            periodo_atual = "apos_expediente"
    
    logger.info(f"[VALIDAÇÃO] Hora atual: {hora_atual_str}, Período determinado: {periodo_atual}")
    
    # Inicializar status padrão como "pontual"
    status_registro = "Pontual"
    
    # Validar cada tipo de registro com base no período atual
    if tipo_registro == 'entrada_manha':
        # Entrada manhã é permitida a partir do horário de entrada menos tolerância
        if entrada_manha is None:
            return {'permitido': False, 'mensagem': 'Horário de entrada não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO: Verificar se o registro será considerado "Atrasado" ou "Pontual"
        # Horário limite de tolerância para ser considerado pontual
        hora_limite_pontualidade = datetime.combine(date.today(), entrada_manha)
        hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()

        # Determinar status baseado no horário
        if hora_atual_obj < entrada_manha:
            status_registro = "Pontual"  # Entrada antecipada é considerada pontual
        elif hora_atual_obj <= hora_limite_pontualidade:
            status_registro = "Pontual"  # Dentro da tolerância
        else:
            status_registro = "Atrasado"  # Após tolerância
        
        # ✅ CORREÇÃO: Não permitir registro antes do horário programado
        # Permitir registro apenas a partir do horário programado (sem tolerância antes)
        hora_min = datetime.combine(date.today(), entrada_manha)
        hora_min = hora_min.time()  # Não aplicar tolerância negativa
        
        # ✅ CORREÇÃO CRÍTICA: SISTEMA NÃO DEVE BLOQUEAR FUNCIONÁRIO APÓS TOLERÂNCIA
        # Conforme solicitação: "deve permitir e marcar atraso e contar o horário de inicio de trabalho"
        # REMOVIDO: limite_atraso que bloqueava registro após 60 minutos

        # Entrada manhã é mais apropriada no início do dia
        if periodo_atual in ["antes_expediente", "manha"]:
            # ✅ FLEXIBILIDADE TOTAL: Permitir entrada antecipada com limite razoável
            # Definir limite mínimo para entrada antecipada (2 horas antes é razoável)
            limite_antecipacao = 120  # 120 minutos (2 horas) antes do horário programado
            hora_min_antecipada = datetime.combine(date.today(), entrada_manha)
            hora_min_antecipada = (hora_min_antecipada - timedelta(minutes=limite_antecipacao)).time()

            # Apenas bloquear se for MUITO antecipado (mais de 2h antes)
            if hora_atual_obj < hora_min_antecipada:
                status_registro = 'Muito_Antecipado'
                return {
                    'permitido': True,  # ✅ MUDANÇA: Permitir mesmo assim, mas alertar
                    'mensagem': f"Entrada muito antecipada (recomendado após {hora_min_antecipada.strftime('%H:%M')})",
                    'horario_liberado': True,
                    'status': status_registro
                }

            # ✅ CORREÇÃO CRÍTICA: SEMPRE PERMITIR REGISTRO APÓS HORÁRIO PROGRAMADO
            # Sistema deve permitir e marcar como atraso, nunca bloquear
            return {
                'permitido': True,
                'mensagem': f"Entrada registrada com sucesso ({status_registro})",
                'horario_liberado': True,
                'status': status_registro
            }
        else:
            # ✅ CORREÇÃO CRÍTICA: PERMITIR REGISTRO MESMO FORA DO PERÍODO IDEAL
            # Sistema deve permitir e marcar como atraso, conforme solicitação do usuário
            return {
                'permitido': True,
                'mensagem': f"Entrada registrada fora do período ideal ({status_registro})",
                'horario_liberado': True,
                'status': status_registro
            }
    
    elif tipo_registro == 'saida_almoco':
        # ✅ NOVA LÓGICA FLEXÍVEL: Batida 2 (Início do Intervalo) - SEMPRE PERMITIDA
        # Horário de almoço configurado é apenas SIMBÓLICO/INFORMATIVO

        # Verificar se já existe entrada da manhã (batida 1)
        data_atual = date.today()
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT COUNT(*) as count
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            AND tipo_registro = 'entrada_manha'
        """, (funcionario_id, data_atual))

        entrada_existe = cursor.fetchone()['count'] > 0
        cursor.close()
        conn.close()

        if not entrada_existe:
            return {
                'permitido': False,
                'mensagem': 'É necessário registrar a entrada da manhã antes do início do intervalo',
                'horario_liberado': False,
                'status': None
            }

        # ✅ REGRA FUNDAMENTAL: NUNCA BLOQUEAR FUNCIONÁRIO - SEMPRE PERMITIR E APLICAR REGRAS
        # Conforme solicitação: "A REGRA DO SISTEMA É NUNCA IMPEDIR QUE FUNCIONARIO BATA O PONTO"

        hora_minima_intervalo = time(10, 0)  # 10:00 - horário recomendado para intervalo
        status_registro = 'Pontual'
        mensagem = "Saída para intervalo registrada com sucesso"

        # Se for muito cedo, apenas alertar mas SEMPRE PERMITIR
        if hora_atual_obj < hora_minima_intervalo:
            status_registro = 'Antecipado'
            mensagem = f"Saída para intervalo antecipada (recomendado após {hora_minima_intervalo.strftime('%H:%M')})"

        # ✅ SEMPRE PERMITIDO - NUNCA BLOQUEAR
        return {
            'permitido': True,
            'mensagem': mensagem,
            'horario_liberado': True,
            'status': status_registro
        }
    
    elif tipo_registro == 'entrada_tarde':
        # ✅ NOVA LÓGICA FLEXÍVEL: Batida 3 (Retorno do Intervalo) - FLEXÍVEL COM REGRA DE 1H
        # Horário de retorno configurado é apenas SIMBÓLICO/INFORMATIVO

        # Verificar se existe saída para almoço (batida 2)
        data_atual = date.today()
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT data_hora
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            AND tipo_registro = 'saida_almoco'
            ORDER BY data_hora DESC
            LIMIT 1
        """, (funcionario_id, data_atual))

        saida_almoco_registro = cursor.fetchone()
        cursor.close()
        conn.close()

        if not saida_almoco_registro:
            return {
                'permitido': False,
                'mensagem': 'É necessário registrar o início do intervalo antes do retorno',
                'horario_liberado': False,
                'status': None
            }

        # 🔧 CORREÇÃO: Nova lógica para intervalo
        hora_saida_almoco = saida_almoco_registro['data_hora']
        hora_atual_datetime = datetime.combine(date.today(), hora_atual_obj)

        # Calcular diferença em minutos
        diferenca_minutos = (hora_atual_datetime - hora_saida_almoco).total_seconds() / 60

        # ✅ REGRA CRÍTICA: 1 HORA OBRIGATÓRIA DE INTERVALO
        if diferenca_minutos < 60:  # Menos de 1 hora
            minutos_faltantes = 60 - int(diferenca_minutos)
            # ✅ ARREDONDAMENTO AUTOMÁTICO: Ajustar para 1 hora completa
            hora_ajustada = hora_saida_almoco + timedelta(hours=1)

            logger.warning(f"[VOLTA PRECOCE INTERVALO] Funcionário {funcionario_id}: Tentativa de retorno após {int(diferenca_minutos)} minutos. Ajustando para 1 hora completa.")

            return {
                'permitido': True,
                'mensagem': f'Intervalo ajustado automaticamente para 1 hora completa. Horário original: {hora_atual_obj.strftime("%H:%M")}, Horário ajustado: {hora_ajustada.strftime("%H:%M")}',
                'horario_liberado': True,
                'status': 'Pontual',
                'hora_ajustada': hora_ajustada,
                'compensacao_intervalo': minutos_faltantes,
                'log_tipo': 'VOLTA_PRECOCE_INTERVALO'
            }

        # ✅ REGRA: INTERVALO MAIOR QUE 1 HORA (ATRASO)
        elif diferenca_minutos > 60:  # Mais de 1 hora
            minutos_excesso = int(diferenca_minutos - 60)

            logger.warning(f"[ATRASO INTERVALO] Funcionário {funcionario_id}: Retorno após {int(diferenca_minutos)} minutos. Excesso de {minutos_excesso} minutos.")

            return {
                'permitido': True,
                'mensagem': f'Atraso no retorno do intervalo: {minutos_excesso} minutos além da 1 hora obrigatória',
                'horario_liberado': True,
                'status': 'Atrasado',
                'minutos_atraso': minutos_excesso,
                'log_tipo': 'ATRASO_INTERVALO'
            }

        # ✅ INTERVALO EXATO DE 1 HORA
        else:
            return {
                'permitido': True,
                'mensagem': 'Retorno do intervalo pontual (1 hora completa)',
                'horario_liberado': True,
                'status': 'Pontual'
            }
        
    elif tipo_registro == 'saida':
        # Saída é permitida a partir do horário configurado
        if saida is None:
            return {'permitido': False, 'mensagem': 'Horário de saída não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO: Verificar status (sempre pontual para saída, pois saída antes é considerada pontual)
        status_registro = "Pontual"
        
        # ✅ CORREÇÃO: Saída só deve ser permitida a partir do horário programado (ou tolerância antes)
        hora_min = datetime.combine(date.today(), saida)
        hora_min = (hora_min - timedelta(minutes=tolerancia)).time()
        
        # ✅ CORREÇÃO: Definir um limite máximo para registro de saída
        limite_atraso = 120  # 2 horas de limite para registrar saída (aumentado para melhor flexibilidade)
        hora_max = datetime.combine(date.today(), saida)
        hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()
        
        # ✅ CORREÇÃO: Verificar se hora atual está após o início da janela de tolerância
        if hora_atual_obj >= hora_min:
            # Hora atual está após o horário mínimo permitido para saída
            # ✅ CORREÇÃO: Verificar se passou do limite máximo permitido
            if hora_atual_obj > hora_max:
                return {
                    'permitido': False,
                    'mensagem': f"Horário de saída expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                    'horario_liberado': False,
                    'status': None
                }
            return {
                'permitido': True, 
                'mensagem': f"Saída registrada com sucesso ({status_registro})", 
                'horario_liberado': True,
                'status': status_registro
            }
        else:
            # Antes do horário permitido - não liberar ainda
            return {
                'permitido': False, 
                'mensagem': f"Saída ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                'horario_liberado': False,
                'status': None
            }
    
    else:
        return {'permitido': False, 'mensagem': f'Tipo de registro não reconhecido: {tipo_registro}', 'horario_liberado': False, 'status': None}

def verificar_registros_ausentes(funcionario_id):
    """
    Verifica horários que não foram registrados e que já passaram do limite de tempo.
    Identifica ausências nos registros de ponto do funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        list: Lista de tipos de registro ausentes
    """
    try:
        # Obter data atual
        data_atual = date.today()
        data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
        data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
        
        # ✅ MELHORIA: Verificar a data de cadastro do funcionário
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT 
                DATE(data_cadastro) AS data_cadastro, 
                nome_completo
            FROM funcionarios
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        info_funcionario = cursor.fetchone()
        
        if not info_funcionario:
            logger.warning(f"Funcionário {funcionario_id} não encontrado ou inativo ao verificar ausências")
            conn.close()
            return []
        
        # ✅ MELHORIA: Não verificar ausências no dia do cadastro
        data_cadastro = info_funcionario['data_cadastro'].date() if hasattr(info_funcionario['data_cadastro'], 'date') else info_funcionario['data_cadastro']
        
        if data_cadastro == data_atual:
            logger.info(f"Funcionário {info_funcionario['nome_completo']} cadastrado hoje. Ignorando verificação de ausências.")
            conn.close()
            return []
        
        # Obter horários do funcionário
        horarios = obter_horarios_funcionario(funcionario_id)
        
        # Obter hora atual
        hora_atual = obter_hora_atual(fuso_horario_manaus=True)
        hora_atual_obj = hora_atual.time()
        
        # Obter registros do dia
        cursor.execute("""
            SELECT tipo_registro, data_hora
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND data_hora BETWEEN %s AND %s
        """, (funcionario_id, data_inicio, data_fim))
        
        registros = cursor.fetchall()
        
        # Converter registros para conjunto de tipos para facilitar a verificação
        tipos_registrados = {reg['tipo_registro'] for reg in registros}
        
        # Lista de ausências
        ausencias = []
        
        # Verificar cada tipo de registro
        def string_para_time(hora_str):
            if not hora_str:
                return None
            try:
                horas, minutos = map(int, hora_str.split(':'))
                return time(horas, minutos)
            except (ValueError, AttributeError):
                return None
        
        # Definir a sequência de tipos na ordem correta
        sequencia = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
        
        # ✅ CORREÇÃO: Definir limites de tempo para cada tipo de registro
        limites_atraso = {
            'entrada_manha': 60,   # 1 hora após o horário programado
            'saida_almoco': 60,    # 1 hora após o horário programado
            'entrada_tarde': 120,  # 2 horas após o horário programado
            'saida': 15            # 15 minutos após o horário programado
        }
        
        # Para cada tipo na sequência, verificar se:
        # 1. O horário está configurado
        # 2. O horário já passou (considerando a tolerância)
        # 3. Não foi registrado
        for tipo in sequencia:
            # Obter o horário configurado para este tipo
            horario_str = None
            if tipo == 'entrada_manha':
                horario_str = horarios.get('entrada_manha')
            elif tipo == 'saida_almoco':
                horario_str = horarios.get('saida_almoco')
            elif tipo == 'entrada_tarde':
                horario_str = horarios.get('entrada_tarde')
            elif tipo == 'saida':
                horario_str = horarios.get('saida')
                
            if not horario_str:
                continue  # Se não há horário configurado, pular
            
            horario_time = string_para_time(horario_str)
            if not horario_time:
                continue  # Se horário inválido, pular
            
            # ✅ CORREÇÃO: Usar o limite de tempo específico para cada tipo de registro
            limite_atraso = limites_atraso.get(tipo, 30)  # Padrão de 30 minutos se não especificado
            
            # Adicionar limite de atraso ao horário programado
            horario_limite = datetime.combine(data_atual, horario_time) + timedelta(minutes=limite_atraso)
            
            # ✅ MELHORIA: Se já passou do horário limite e não foi registrado, 
            # marcar como ausente independentemente dos outros registros
            if hora_atual > horario_limite and tipo not in tipos_registrados:
                ausencias.append({
                    'tipo': tipo,
                    'horario_previsto': horario_str,
                    'status': 'AUSENTE',
                    'limite_registro': horario_limite.strftime('%H:%M')
                })
        
        conn.close()
        
        if len(ausencias) > 0:
            logger.info(f"[AUSÊNCIAS] Detectadas {len(ausencias)} ausências para funcionário {funcionario_id}: {ausencias}")
        
        return ausencias
        
    except Exception as e:
        logger.error(f"Erro ao verificar registros ausentes: {str(e)}")
        return []

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@registro_ponto_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico():
    """
    Página de registro de ponto biométrico.
    Interface para captura biométrica via ZK4500.
    """
    try:
        context = {
            'titulo': 'Registro de Ponto Biométrico',
            'usuario_logado': session.get('usuario', 'Usuário'),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M')
        }
        
        return render_template('registro_ponto/biometrico.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar página biométrica: {str(e)}")
        # Corrigir redirecionamento para função correta
        return redirect(url_for('funcionarios.index'))

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico_underscore():
    """Versão duplicada da rota biometrico com underscore"""
    return pagina_registro_biometrico()

@registro_ponto_bp.route('/manual')
@require_login
def pagina_registro_manual():
    """
    Página de registro de ponto manual.
    Lista funcionários para seleção e registro manual.
    """
    try:
        logger.info(f"[DEBUG] Iniciando pagina_registro_manual para usuário: {session.get('usuario', 'desconhecido')}")
        
        # Verificar e criar tabela se necessário
        if not verificar_e_criar_tabela_registros():
            logger.error("[DEBUG] Falha ao verificar/criar tabela registros_ponto")
            
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        logger.info(f"[DEBUG] Nível de acesso: {nivel_acesso}")
        
        # Obter lista de funcionários ativos
        logger.info("[DEBUG] Iniciando conexão com banco de dados")
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        logger.info("[DEBUG] Conexão com banco estabelecida")
        
        logger.info("[DEBUG] Executando query para buscar funcionários")
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                f.cpf,
                f.matricula_empresa,
                f.cargo,
                CASE 
                    WHEN f.setor_obra IS NULL OR f.setor_obra = '' THEN 
                        CASE 
                            WHEN f.setor IS NULL OR f.setor = '' THEN 'Não informado'
                            ELSE f.setor
                        END
                    ELSE f.setor_obra
                END as setor,
                f.foto_3x4,
                COALESCE(e.nome_fantasia, 'Empresa Padrão') AS empresa,
                COALESCE(ht.nome_horario, 'Horário Padrão') as nome_horario
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.ativo = TRUE
            ORDER BY f.nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        logger.info(f"[DEBUG] Query executada com sucesso. {len(funcionarios_raw)} funcionários encontrados")
        conn.close()
        logger.info("[DEBUG] Conexão com banco fechada")
        
        # Processar lista de funcionários
        logger.info("[DEBUG] Iniciando processamento da lista de funcionários")
        funcionarios = []
        for i, f in enumerate(funcionarios_raw):
            try:
                # Validar se o CPF existe antes de tentar mascarar
                cpf_original = f['cpf'] if f['cpf'] else ''
                cpf_exibicao = ''
                
                if cpf_original:
                    try:
                        from utils.helpers import mascarar_cpf
                        cpf_exibicao = mascarar_cpf(cpf_original) if nivel_acesso != 'admin' else cpf_original
                    except Exception as e:
                        logger.warning(f"[DEBUG] Erro ao mascarar CPF para funcionário {f['nome_completo']}: {str(e)}")
                        cpf_exibicao = '***.***.***-**'
                else:
                    cpf_exibicao = 'CPF não informado'
                
                # O setor já está tratado na consulta SQL com CASE WHEN
                funcionario = {
                    'id': f['id'],
                    'nome_completo': f['nome_completo'] or 'Nome não informado',
                    'cpf': cpf_original,
                    'cpf_exibicao': cpf_exibicao,
                    'matricula_empresa': f['matricula_empresa'] or 'Sem matrícula',
                    'cargo': f['cargo'] or 'Não informado',
                    'setor': f['setor'],  # Já está tratado na consulta SQL
                    'foto_url': normalizar_caminho_foto(f['foto_3x4']),
                    'empresa': f['empresa'],
                    'horario_trabalho': f['nome_horario']
                }
                funcionarios.append(funcionario)
                if i < 3:  # Log apenas os primeiros 3 para não sobrecarregar
                    logger.info(f"[DEBUG] Funcionário {i+1} processado: {funcionario['nome_completo']}")
            except Exception as e:
                logger.error(f"[DEBUG] Erro ao processar funcionário {i+1}: {str(e)}")
                continue
                
        logger.info(f"[DEBUG] Lista de funcionários processada. Total: {len(funcionarios)}")
        
        logger.info("[DEBUG] Montando contexto do template")
        context = {
            'titulo': 'Registro de Ponto Manual',
            'funcionarios': funcionarios,
            'total_funcionarios': len(funcionarios),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M'),
            'nivel_acesso': nivel_acesso
        }
        logger.info(f"[DEBUG] Contexto montado com {len(context)} variáveis")
        
        logger.info("[DEBUG] Tentando renderizar template registro_ponto/manual.html")
        resultado = render_template('registro_ponto/manual.html', **context)
        logger.info("[DEBUG] Template renderizado com sucesso")
        
        return resultado
        
    except Exception as e:
        error_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.error(f"[ERROR] {error_id} - Erro ao carregar página manual: {str(e)}")
        logger.error(f"[ERROR] {error_id} - Traceback: ", exc_info=True)
        
        # Retornar página de erro personalizada em vez de redirect
        return render_template('erro.html', 
                             codigo=500,
                             titulo="Erro interno do sistema",
                             mensagem=f"Ocorreu um erro inesperado. ID: {error_id}",
                             error_id=error_id), 500

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/manual')
@require_login
def pagina_registro_manual_underscore():
    """Versão duplicada da rota manual com underscore"""
    return pagina_registro_manual()

# ========================================
# APIs DE REGISTRO
# ========================================

@registro_ponto_bp.route('/api/identificar-funcionario-biometria', methods=['POST'])
@require_login
def api_identificar_funcionario_biometria():
    """
    API para identificar funcionário pela biometria.
    Recebe template biométrico e retorna dados do funcionário se encontrado.
    """
    try:
        dados = request.get_json()
        template_biometrico = dados.get('template_biometrico')
        qualidade_biometria = dados.get('qualidade_biometria', 0)

        if not template_biometrico:
            return jsonify({
                'success': False,
                'message': 'Template biométrico não fornecido'
            }), 400

        # Buscar funcionário pela biometria
        funcionario = buscar_funcionario_por_biometria(template_biometrico)

        if not funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não identificado. Biometria não cadastrada.'
            }), 404

        # Buscar registros do dia
        registros_hoje = obter_registros_funcionario_hoje(funcionario['id'])

        # Buscar jornada de trabalho
        jornada = obter_jornada_funcionario_real(funcionario['id'])

        return jsonify({
            'success': True,
            'funcionario': {
                'id': funcionario['id'],
                'nome_completo': funcionario['nome_completo'],
                'cargo': funcionario['cargo'],
                'matricula_empresa': funcionario['matricula_empresa'],
                'empresa_nome': funcionario.get('empresa_nome', 'N/A'),
                'cnpj': funcionario.get('cnpj', 'N/A'),
                'setor': funcionario.get('setor', 'N/A'),
                'foto_url': funcionario.get('foto_url', '/static/images/default-avatar.png'),
                'jornada': jornada,
                'registros_hoje': registros_hoje
            },
            'qualidade_biometria': qualidade_biometria
        })

    except Exception as e:
        logger.error(f"Erro ao identificar funcionário por biometria: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do servidor'
        }), 500

@registro_ponto_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico():
    """
    API para processar registro de ponto biométrico.
    Valida dados, horários e registra ponto biométrico no banco.
    """
    try:
        dados = request.get_json()
        
        # Validar dados obrigatórios
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        qualidade_biometria = dados.get('qualidade_biometria')
        observacoes = dados.get('observacoes', '')
        
        if not all([funcionario_id, tipo_registro]):
            return jsonify({
                'success': False,
                'message': 'Dados obrigatórios não informados'
            }), 400
        
        # Validar se funcionário existe e está ativo
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, ativo 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,))
        
        funcionario = cursor.fetchone()
        conn.close()
        
        if not funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        if not funcionario['ativo']:
            return jsonify({
                'success': False,
                'message': 'Funcionário inativo'
            }), 400
        
        # Validar duplicata
        data_atual = date.today()
        duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        
        if duplicata['existe']:
            return jsonify({
                'success': False,
                'message': duplicata['mensagem'],
                'registro_existente': duplicata['registro_existente']
            }), 400
        
        # ✅ VALIDAÇÃO - Verificar se funcionário tem horário configurado
        horarios_funcionario = obter_horarios_funcionario(funcionario_id)

        # ✅ CORREÇÃO: Impedir registro se funcionário não tem horário configurado
        if not horarios_funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não possui horário de trabalho configurado. Entre em contato com o administrador.'
            }), 400

        # ✅ VALIDAÇÃO - Verificar se tipo de registro é permitido no horário atual
        validacao_horario = validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario, funcionario_id)

        if not validacao_horario['permitido']:
            return jsonify({
                'success': False,
                'message': validacao_horario['mensagem']
            }), 400
        
        # ✅ MELHORIA: Usar diretamente as observações informadas pelo usuário, sem adicionar o horário
        # Registrar ponto biométrico
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='biometrico',
            observacoes=observacoes,  # Observações exatamente como fornecidas pelo usuário
            qualidade_biometria=qualidade_biometria
        )
        
        if resultado['success']:
            return jsonify({
                'success': True,
                'message': resultado['message'],
                'registro_id': resultado['registro_id'],
                'funcionario_nome': funcionario['nome_completo'],
                'data_hora': datetime.now().strftime('%d/%m/%Y %H:%M')
            })
        else:
            return jsonify({
                'success': False,
                'message': resultado['message']
            }), 500
        
    except Exception as e:
        logger.error(f"Erro no registro biométrico: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_biometrico()

@registro_ponto_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual():
    """
    API para registrar ponto manual.
    """
    try:
        # Obter dados do formulário
        funcionario_id = request.form.get('funcionario_id')
        tipo_registro = request.form.get('tipo_registro')
        observacoes_raw = request.form.get('observacoes', '')

        # 🔧 CORREÇÃO DE ENCODING: Garantir que observações estejam em UTF-8 correto
        observacoes = corrigir_encoding_texto(observacoes_raw) if observacoes_raw else ''

        # Validar dados
        if not funcionario_id or not tipo_registro:
            return jsonify({'success': False, 'message': 'Dados incompletos'}), 400
        
        funcionario_id = int(funcionario_id)
        
        # Obter horários do funcionário
        horarios_funcionario = obter_horarios_funcionario(funcionario_id)
        
        # Verificar duplicata antes de validar o horário para melhorar feedback
        data_atual = date.today()
        resultado_duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        if resultado_duplicata['existe']:
            return jsonify({
                'success': False, 
                'message': resultado_duplicata['mensagem'],
                'registro_existente': resultado_duplicata['registro_existente'],
                'code': 'DUPLICATE_RECORD'  # Código para auxiliar o front-end
            }), 400
        
        # Validar tipo de registro pelo horário
        validacao = validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario, funcionario_id)
        if not validacao['permitido']:
            return jsonify({
                'success': False, 
                'message': validacao['mensagem'],
                'code': 'NOT_ALLOWED_TIME'  # Código para auxiliar o front-end
            }), 400
        
        # ✅ CORREÇÃO: Obter o status de pontualidade (Pontual/Atrasado)
        status_pontualidade = validacao.get('status')

        # ✅ NOVA REGRA: Verificar se há horário ajustado para intervalo
        hora_registro = None
        log_adicional = ""

        if validacao.get('hora_ajustada'):
            # Usar horário ajustado para volta precoce do intervalo
            hora_registro = validacao['hora_ajustada']
            log_adicional = f" [AJUSTE AUTOMÁTICO: {validacao.get('log_tipo', 'SISTEMA')}]"
            logger.info(f"[REGISTRO AJUSTADO] Funcionário {funcionario_id}: Horário ajustado de {obter_hora_atual(fuso_horario_manaus=True).strftime('%H:%M')} para {hora_registro.strftime('%H:%M')}")

        # ✅ CORREÇÃO: Obter hora atual para usar na resposta
        hora_atual = obter_hora_atual(fuso_horario_manaus=True)

        # Registrar ponto com o status de pontualidade e horário ajustado se necessário
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='manual',
            observacoes=(observacoes or "") + log_adicional,
            status_pontualidade=status_pontualidade,
            hora_customizada=hora_registro  # ✅ NOVA FUNCIONALIDADE: Horário customizado
        )
        
        if not resultado['success']:
            return jsonify(resultado), 500
        
        # ✅ MELHORIA: Calcular e atualizar horas trabalhadas, horas extras e banco de horas
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            # Obter todos os registros do dia para o funcionário
            data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
            data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
            
            cursor.execute("""
                SELECT id, funcionario_id, tipo_registro, data_hora, metodo_registro
                FROM registros_ponto
                WHERE funcionario_id = %s AND data_hora BETWEEN %s AND %s
                ORDER BY data_hora ASC
            """, (funcionario_id, data_inicio, data_fim))
            
            registros_dia = cursor.fetchall()
            
            # Obter informações do funcionário para cálculo de horas extras e banco de horas
            cursor.execute("""
                SELECT 
                    id, nome_completo, 
                    jornada_seg_qui_entrada, jornada_seg_qui_saida,
                    jornada_intervalo_entrada, jornada_intervalo_saida,
                    banco_horas, hora_extra
                FROM funcionarios
                WHERE id = %s
            """, (funcionario_id,))
            
            funcionario_info = cursor.fetchone()
            
            # Calcular horas trabalhadas, extras e banco de horas
            resultado_horas = calcular_horas_trabalhadas(registros_dia, funcionario_info)
            
            # Se o dia está completo, atualizar horas extras e banco de horas
            if resultado_horas['completo']:
                # Inserir ou atualizar na tabela banco_horas para o dia atual
                data_referencia = datetime.now().date()

                # Calcular valores em minutos com validação
                horas_extras = resultado_horas.get('horas_extras', 0) or 0
                banco_horas = resultado_horas.get('banco_horas', 0) or 0
                horas_trabalhadas = resultado_horas.get('horas_trabalhadas', 0) or 0

                horas_extras_minutos = int(horas_extras * 60) if horas_extras > 0 else 0
                banco_horas_minutos = int(banco_horas * 60) if banco_horas > 0 else 0
                saldo_liquido_minutos = horas_extras_minutos + banco_horas_minutos

                cursor.execute("""
                    INSERT INTO banco_horas
                    (funcionario_id, data_referencia, horas_extras_minutos,
                     saldo_credor_minutos, saldo_liquido_minutos, status_dia, observacoes)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        horas_extras_minutos = VALUES(horas_extras_minutos),
                        saldo_credor_minutos = VALUES(saldo_credor_minutos),
                        saldo_liquido_minutos = VALUES(saldo_liquido_minutos),
                        status_dia = VALUES(status_dia),
                        observacoes = VALUES(observacoes)
                """, (
                    funcionario_id,
                    data_referencia,
                    horas_extras_minutos,
                    banco_horas_minutos,
                    saldo_liquido_minutos,
                    'completo',
                    f"Registro automático - Horas trabalhadas: {horas_trabalhadas:.2f}h"
                ))

                logger.info(f"Banco de horas atualizado para funcionário {funcionario_id}: "
                           f"Horas extras: {resultado_horas['horas_extras']:.2f}h, "
                           f"Banco: {resultado_horas['banco_horas']:.2f}h")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao calcular horas trabalhadas: {str(e)}")
            # Não falhar o registro principal se o cálculo de horas falhar
        
        # ✅ CORREÇÃO: Incluir mais detalhes na resposta
        # Obter nome do funcionário para a resposta
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            cursor.execute("SELECT nome_completo FROM funcionarios WHERE id = %s", (funcionario_id,))
            funcionario = cursor.fetchone()
            nome_funcionario = funcionario['nome_completo'] if funcionario else 'Funcionário'
            
            conn.close()
        except Exception as e:
            logger.error(f"Erro ao buscar nome do funcionário: {str(e)}")
            nome_funcionario = 'Funcionário'
        
        # ✅ NOVA FUNCIONALIDADE: Resposta com informações de ajuste se aplicável
        hora_exibida = hora_registro if hora_registro else hora_atual
        mensagem_base = validacao.get('mensagem', f'Ponto registrado com sucesso ({status_pontualidade})')

        resposta = {
            'success': True,
            'message': mensagem_base,
            'registro_id': resultado['registro_id'],
            'status_pontualidade': status_pontualidade,
            'hora': hora_exibida.strftime('%H:%M:%S'),
            'data_hora': hora_exibida.strftime('%Y-%m-%d %H:%M:%S'),
            'funcionario': {
                'id': funcionario_id,
                'nome': nome_funcionario
            },
            'tipo_registro': tipo_registro,
            'tipo_registro_formatado': tipo_registro.replace('_', ' ').title(),
            'horas_calculadas': resultado_horas if 'resultado_horas' in locals() else None
        }

        # ✅ LOGS ESPECÍFICOS: Adicionar informações de ajuste se aplicável
        if validacao.get('log_tipo'):
            resposta['ajuste_automatico'] = {
                'tipo': validacao['log_tipo'],
                'hora_original': hora_atual.strftime('%H:%M:%S'),
                'hora_ajustada': hora_exibida.strftime('%H:%M:%S'),
                'motivo': validacao.get('mensagem', ''),
                'compensacao_minutos': validacao.get('compensacao_intervalo', 0),
                'atraso_minutos': validacao.get('minutos_atraso', 0)
            }

        return jsonify(resposta)
        
    except Exception as e:
        logger.error(f"Erro ao registrar ponto manual: {str(e)}")
        return jsonify({'success': False, 'message': f'Erro ao registrar ponto: {str(e)}'}), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_manual()

@registro_ponto_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios(funcionario_id):
    """
    API para obter horários de trabalho de um funcionário.
    Usado no modal de registro manual.
    Filtra tipos de registro disponíveis com base no horário atual.
    """
    try:
        horarios = obter_horarios_funcionario(funcionario_id)
        
        # Converter times para strings
        horarios_formatados = {
            'entrada_manha': horarios['entrada_manha'],
            'saida_almoco': horarios['saida_almoco'],
            'entrada_tarde': horarios['entrada_tarde'],
            'saida': horarios['saida'],
            'tolerancia_minutos': horarios['tolerancia_minutos'],
            'nome_horario': horarios['nome_horario']
        }
        
        # ✅ MELHORIA: Adicionar informações de tolerância explícitas
        horarios_com_tolerancia = {}
        tolerancia = horarios['tolerancia_minutos']
        
        # Calcular horários com tolerância
        for tipo, horario_str in {
            'entrada_manha': horarios['entrada_manha'],
            'saida_almoco': horarios['saida_almoco'],
            'entrada_tarde': horarios['entrada_tarde'],
            'saida': horarios['saida']
        }.items():
            if not horario_str:
                continue
                
            try:
                # Converter string para objeto time
                horas, minutos = map(int, horario_str.split(':'))
                horario_time = time(horas, minutos)
                
                # ✅ REGRA OBRIGATÓRIA: Entrada da manhã (horário + tolerância)
                if tipo == 'entrada_manha':
                    horario_com_tolerancia = datetime.combine(date.today(), horario_time) + timedelta(minutes=tolerancia)
                    horario_tolerancia_str = horario_com_tolerancia.strftime('%H:%M')
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': horario_tolerancia_str,
                        'mensagem': f"Permitido até {horario_tolerancia_str}"
                    }
                # ✅ REGRA OBRIGATÓRIA: Saída final (horário + tolerância)
                elif tipo == 'saida':
                    horario_com_tolerancia = datetime.combine(date.today(), horario_time) + timedelta(minutes=tolerancia)
                    horario_tolerancia_str = horario_com_tolerancia.strftime('%H:%M')
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': horario_tolerancia_str,
                        'mensagem': f"Permitido até {horario_tolerancia_str}"
                    }
                # ✅ SIMBÓLICO: Saída almoço (flexível, apenas informativo)
                elif tipo == 'saida_almoco':
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': 'Flexível',
                        'mensagem': f"Horário simbólico - Flexível após entrada"
                    }
                # ✅ SIMBÓLICO: Entrada tarde (flexível, apenas regra de 1h)
                elif tipo == 'entrada_tarde':
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': 'Flexível',
                        'mensagem': f"Horário simbólico - Mínimo 1h de intervalo"
                    }
            except (ValueError, TypeError, AttributeError) as e:
                logger.warning(f"Erro ao calcular tolerância para {tipo}: {e}")
        
        # ✅ LÓGICA FLEXÍVEL: Obter registros existentes para determinar próxima batida
        data_atual = date.today()
        registros_existentes = obter_batidas_do_dia(funcionario_id, data_atual)
        tipos_ja_registrados = [r['tipo_registro'] for r in registros_existentes]

        # Sequência correta das 4 batidas
        sequencia_jornada = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']

        # Gerar opções de tipos de registro baseadas nos horários
        todos_tipos = []
        tipos_liberados = []

        # ✅ CORREÇÃO: Remover hora_atual do servidor - será capturada em tempo real no frontend
        # hora_atual = obter_hora_atual().strftime('%H:%M')  # REMOVIDO

        # ✅ CORREÇÃO CRÍTICA: USAR APENAS CLASSIFICAÇÃO INTELIGENTE
        turno_info = {'turno': 'Manhã'}  # Padrão, pode ser melhorado
        num_batidas = len(registros_existentes)
        hora_atual = datetime.now()

        # ✅ LÓGICA UNIFICADA: Usar apenas classificar_batida_inteligente()
        proximo_tipo = classificar_batida_inteligente(funcionario_id, num_batidas + 1, turno_info, hora_atual)

        logger.info(f"[API HORÁRIOS] Funcionário: {funcionario_id}, Registros: {num_batidas}, Hora: {hora_atual.strftime('%H:%M')}, Próximo tipo: {proximo_tipo}")

        # ✅ VALIDAÇÃO: Se não conseguiu determinar tipo, retornar erro
        if proximo_tipo is None:
            logger.error(f"[API HORÁRIOS] Não foi possível determinar próximo tipo para funcionário {funcionario_id}")
            return jsonify({
                'success': False,
                'message': 'Não foi possível determinar o tipo de registro. Verifique se o funcionário tem horário configurado.'
            }), 400

        # ✅ LÓGICA SIMPLIFICADA: Criar todos os tipos para referência
        mapeamento_tipos = {
            'entrada_manha': {
                'text': f'Entrada Manhã ({horarios_formatados["entrada_manha"]})',
                'tolerancia': horarios_com_tolerancia.get('entrada_manha', {}).get('mensagem', ''),
                'icone': 'fa-sign-in-alt'
            },
            'saida_almoco': {
                'text': f'Saída Intervalo ({horarios_formatados["saida_almoco"]})',
                'tolerancia': horarios_com_tolerancia.get('saida_almoco', {}).get('mensagem', ''),
                'icone': 'fa-pause'
            },
            'entrada_tarde': {
                'text': f'Retorno Intervalo ({horarios_formatados["entrada_tarde"]})',
                'tolerancia': horarios_com_tolerancia.get('entrada_tarde', {}).get('mensagem', ''),
                'icone': 'fa-play'
            },
            'saida': {
                'text': f'Saída Final ({horarios_formatados["saida"]})',
                'tolerancia': horarios_com_tolerancia.get('saida', {}).get('mensagem', ''),
                'icone': 'fa-sign-out-alt'
            }
        }

        # ✅ CRIAR TODOS OS TIPOS PARA REFERÊNCIA
        for tipo_key, tipo_info in mapeamento_tipos.items():
            if horarios.get(tipo_key.replace('_', '_')):  # Verificar se horário existe
                todos_tipos.append({
                    'value': tipo_key,
                    'text': tipo_info['text'],
                    'tolerancia': tipo_info['tolerancia'],
                    'icone': tipo_info['icone']
                })

        # ✅ LÓGICA CORRIGIDA: OFERECER APENAS O TIPO DETERMINADO PELA FUNÇÃO INTELIGENTE
        if proximo_tipo and proximo_tipo in mapeamento_tipos:
            # Verificar se já foi registrado
            if proximo_tipo not in tipos_ja_registrados:
                tipos_liberados.append({
                    'value': proximo_tipo,
                    'text': mapeamento_tipos[proximo_tipo]['text'],
                    'tolerancia': mapeamento_tipos[proximo_tipo]['tolerancia'],
                    'icone': mapeamento_tipos[proximo_tipo]['icone']
                })
                logger.info(f"[API HORÁRIOS] Oferecendo tipo: {proximo_tipo} baseado na lógica inteligente")
            else:
                logger.warning(f"[API HORÁRIOS] Tipo {proximo_tipo} já foi registrado hoje")
        else:
            logger.warning(f"[API HORÁRIOS] Tipo {proximo_tipo} não encontrado no mapeamento ou é None")
        
        # ✅ MELHORIA: Verificar registros já existentes para hoje
        registros_existentes = []
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            data_atual = date.today()
            data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
            data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
            
            cursor.execute("""
                SELECT 
                    tipo_registro, 
                    DATE_FORMAT(data_hora, '%H:%i') as hora,
                    status_pontualidade
                FROM registros_ponto 
                WHERE funcionario_id = %s 
                AND data_hora BETWEEN %s AND %s
                ORDER BY data_hora ASC
            """, (funcionario_id, data_inicio, data_fim))
            
            for registro in cursor.fetchall():
                registros_existentes.append({
                    'tipo_registro': registro['tipo_registro'],
                    'hora': registro['hora'],
                    'status_pontualidade': registro['status_pontualidade'] or 'Registrado'
                })
                
                # Remover da lista de tipos disponíveis
                tipos_liberados = [t for t in tipos_liberados if t['value'] != registro['tipo_registro']]
            
            conn.close()
        except Exception as e:
            logger.error(f"Erro ao verificar registros existentes: {str(e)}")
        
        # ✅ MELHORIA: Verificar registros ausentes
        ausencias = verificar_registros_ausentes(funcionario_id)
        
        return jsonify({
            'success': True,
            'horarios': horarios_formatados,
            'horarios_com_tolerancia': horarios_com_tolerancia,
            'tipos_disponiveis': tipos_liberados,
            'todos_tipos': todos_tipos,
            # ✅ CORREÇÃO: Remover hora_atual - será capturada em tempo real no frontend
            # 'hora_atual': hora_atual,  # REMOVIDO
            'ausencias': ausencias,  # Incluir informações sobre ausências
            'registros_existentes': registros_existentes  # Incluir registros existentes
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter horários: {str(e)}'
        }), 500

@registro_ponto_bp.route('/saida_antecipada', methods=['POST'])
@require_login
def saida_antecipada():
    """
    Processar saída antecipada com justificativa
    """
    try:
        logger.info("=== INÍCIO SAÍDA ANTECIPADA ===")
        data = request.get_json()
        logger.info(f"Dados recebidos: {data}")

        funcionario_id = data.get('funcionario_id')
        justificativa_raw = data.get('justificativa', '').strip()

        # 🔧 CORREÇÃO DE ENCODING: Garantir que justificativa esteja em UTF-8 correto
        justificativa = corrigir_encoding_texto(justificativa_raw) if justificativa_raw else ''

        logger.info(f"Funcionário ID: {funcionario_id}, Justificativa: {justificativa}")

        if not funcionario_id:
            return jsonify({
                'success': False,
                'message': 'ID do funcionário é obrigatório'
            }), 400

        if not justificativa:
            return jsonify({
                'success': False,
                'message': 'Justificativa é obrigatória para saída antecipada'
            }), 400

        funcionario_id = int(funcionario_id)
        data_atual = date.today()
        logger.info(f"Data atual: {data_atual}")

        datetime_atual = obter_hora_atual()
        hora_atual = datetime_atual.time()
        logger.info(f"DateTime atual: {datetime_atual}")
        logger.info(f"Hora atual: {hora_atual}")

        # Verificar se já existe registro de saída hoje
        logger.info("Conectando ao banco de dados...")
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        logger.info("Conexão estabelecida com sucesso")

        cursor.execute("""
            SELECT id, tipo_registro, data_hora
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            AND tipo_registro = 'saida'
        """, (funcionario_id, data_atual))

        registro_saida_existente = cursor.fetchone()

        if registro_saida_existente:
            cursor.close()
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Já existe registro de saída para hoje',
                'registro_existente': True
            }), 400

        # Obter informações do funcionário
        cursor.execute("""
            SELECT nome_completo, cargo
            FROM funcionarios
            WHERE id = %s
        """, (funcionario_id,))

        funcionario = cursor.fetchone()
        if not funcionario:
            cursor.close()
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404

        # Registrar saída antecipada
        observacoes_completas = f"SAÍDA ANTECIPADA - {justificativa}"

        cursor.execute("""
            INSERT INTO registros_ponto
            (funcionario_id, tipo_registro, data_hora, metodo_registro, observacoes, criado_por, status_pontualidade)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            funcionario_id,
            'saida',
            datetime.combine(data_atual, hora_atual),
            'manual',
            observacoes_completas,
            session.get('user_id'),
            'Saída Antecipada'
        ))

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"Saída antecipada registrada para funcionário {funcionario_id} - {funcionario['nome_completo']}")

        return jsonify({
            'success': True,
            'message': f'Saída antecipada registrada com sucesso para {funcionario["nome_completo"]}',
            'data_hora': datetime.combine(data_atual, hora_atual).strftime('%d/%m/%Y %H:%M'),
            'status_pontualidade': 'Saída Antecipada',
            'justificativa': justificativa
        })

    except Exception as e:
        logger.error(f"=== ERRO SAÍDA ANTECIPADA ===")
        logger.error(f"Tipo do erro: {type(e).__name__}")
        logger.error(f"Mensagem: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        if 'conn' in locals():
            conn.close()
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

@registro_ponto_bp.route('/atraso_justificado', methods=['POST'])
@require_login
def atraso_justificado():
    """
    Processar entrada com atraso e justificativa opcional
    """
    try:
        logger.info("=== INÍCIO ATRASO JUSTIFICADO ===")
        data = request.get_json()
        logger.info(f"Dados recebidos: {data}")

        funcionario_id = data.get('funcionario_id')
        justificativa_raw = data.get('justificativa', '').strip()
        atraso_minutos = data.get('atraso_minutos', 0)

        # 🔧 CORREÇÃO DE ENCODING: Garantir que justificativa esteja em UTF-8 correto
        justificativa = corrigir_encoding_texto(justificativa_raw) if justificativa_raw else ''

        logger.info(f"Funcionário ID: {funcionario_id}, Justificativa: {justificativa}, Atraso: {atraso_minutos}min")

        if not funcionario_id:
            return jsonify({
                'success': False,
                'message': 'ID do funcionário é obrigatório'
            }), 400

        funcionario_id = int(funcionario_id)
        data_atual = date.today()
        logger.info(f"Data atual: {data_atual}")

        datetime_atual = obter_hora_atual()
        hora_atual = datetime_atual.time()
        logger.info(f"DateTime atual: {datetime_atual}")
        logger.info(f"Hora atual: {hora_atual}")

        # Verificar se já existe registro de entrada hoje
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT COUNT(*) as count
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = %s
            AND tipo_registro = 'entrada_manha'
        """, (funcionario_id, data_atual))

        resultado = cursor.fetchone()
        if resultado['count'] > 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Entrada da manhã já foi registrada hoje'
            }), 400

        # Buscar dados do funcionário
        cursor.execute("""
            SELECT nome_completo
            FROM funcionarios
            WHERE id = %s AND status_cadastro = 'Ativo'
        """, (funcionario_id,))

        funcionario = cursor.fetchone()
        if not funcionario:
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404

        # Registrar entrada com atraso
        if justificativa:
            observacoes_completas = f"ATRASO JUSTIFICADO ({atraso_minutos}min) - {justificativa}"
            status_pontualidade = 'Atraso Justificado'
        else:
            observacoes_completas = f"Atraso de {atraso_minutos} minutos"
            status_pontualidade = 'Atrasado'

        cursor.execute("""
            INSERT INTO registros_ponto
            (funcionario_id, tipo_registro, data_hora, metodo_registro, observacoes, criado_por, status_pontualidade)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            funcionario_id,
            'entrada_manha',
            datetime.combine(data_atual, hora_atual),
            'manual',
            observacoes_completas,
            session.get('usuario', 'sistema'),
            status_pontualidade
        ))

        # Se há justificativa, criar registro na tabela de justificativas
        if justificativa:
            cursor.execute("""
                INSERT INTO justificativas_ponto
                (funcionario_id, data_registro, tipo_justificativa, motivo, status_aprovacao, criado_por, criado_em)
                VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            """, (
                funcionario_id,
                data_atual,
                'atraso',
                justificativa,
                'pendente',
                session.get('user_id', 1)
            ))

        conn.commit()
        conn.close()

        logger.info(f"=== ATRASO JUSTIFICADO REGISTRADO ===")
        logger.info(f"Funcionário: {funcionario['nome_completo']}")
        logger.info(f"Horário: {hora_atual}")
        logger.info(f"Status: {status_pontualidade}")

        message = f'Entrada registrada com sucesso para {funcionario["nome_completo"]}'
        if justificativa:
            message += f' - Justificativa enviada para análise do RH'

        return jsonify({
            'success': True,
            'message': message,
            'data_hora': datetime.combine(data_atual, hora_atual).strftime('%d/%m/%Y %H:%M'),
            'status_pontualidade': status_pontualidade,
            'justificativa': justificativa if justificativa else None,
            'atraso_minutos': atraso_minutos
        })

    except Exception as e:
        logger.error(f"=== ERRO ATRASO JUSTIFICADO ===")
        logger.error(f"Tipo do erro: {type(e).__name__}")
        logger.error(f"Mensagem do erro: {str(e)}")
        logger.error(f"Traceback completo: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'Erro interno do servidor: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios_underscore(funcionario_id):
    """Versão duplicada da API com underscore"""
    return api_obter_horarios(funcionario_id)

@registro_ponto_bp.route('/api/registros/funcionario/<int:funcionario_id>/hoje')
@require_login
def api_registros_funcionario_hoje(funcionario_id):
    """
    API para obter registros de ponto do funcionário no dia atual.
    Usado para exibir lista quando há erro de duplicata.
    """
    try:
        # Obter data atual
        data_atual = date.today()
        data_inicio = f"{data_atual} 00:00:00"
        data_fim = f"{data_atual} 23:59:59"

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Buscar registros do dia
        cursor.execute("""
            SELECT
                id,
                tipo_registro,
                DATE_FORMAT(data_hora, '%H:%i') as hora_registro,
                DATE_FORMAT(data_hora, '%d/%m/%Y %H:%i:%s') as data_hora_completa,
                metodo_registro,
                observacoes,
                IFNULL(status_pontualidade, 'Registrado') as status_pontualidade
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND data_hora BETWEEN %s AND %s
            ORDER BY data_hora ASC
        """, (funcionario_id, data_inicio, data_fim))

        registros = cursor.fetchall()
        conn.close()

        return jsonify({
            'success': True,
            'registros': registros,
            'total': len(registros),
            'data': data_atual.strftime('%d/%m/%Y')
        })

    except Exception as e:
        logger.error(f"Erro ao buscar registros do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao carregar registros do dia',
            'registros': []
        }), 500

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@registro_ponto_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_underscore_bp.errorhandler(404)
def handle_404_underscore(error):
    """Tratamento de erro 404 para o blueprint com underscore."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint registro_ponto: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

@registro_ponto_underscore_bp.route('/saida_antecipada', methods=['POST'])
@require_login
def saida_antecipada_underscore():
    """
    Processar saída antecipada com justificativa (compatibilidade underscore)
    """
    return saida_antecipada()

@registro_ponto_underscore_bp.errorhandler(500)
def handle_500_underscore(error):
    """Tratamento de erro 500 para o blueprint com underscore."""
    logger.error(f"Erro interno no blueprint registro_ponto_underscore: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

def buscar_funcionario_por_biometria(template_biometrico):
    """
    Busca funcionário no banco de dados pela biometria.

    Args:
        template_biometrico (str): Template biométrico para comparação

    Returns:
        dict: Dados do funcionário se encontrado, None caso contrário
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Buscar funcionários com biometria cadastrada
        cursor.execute("""
            SELECT
                f.id,
                f.nome_completo,
                f.cargo,
                f.setor,
                f.matricula_empresa,
                f.digital_dedo1,
                f.digital_dedo2,
                f.foto_url,
                e.razao_social as empresa_nome,
                e.cnpj
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            WHERE f.status_cadastro = 'Ativo'
            AND (f.digital_dedo1 IS NOT NULL OR f.digital_dedo2 IS NOT NULL)
        """)

        funcionarios = cursor.fetchall()
        conn.close()

        # Aqui seria a comparação real dos templates biométricos
        # Por enquanto, simular identificação do primeiro funcionário ativo
        if funcionarios:
            return funcionarios[0]

        return None

    except Exception as e:
        logger.error(f"Erro ao buscar funcionário por biometria: {e}")
        return None

def obter_registros_funcionario_hoje(funcionario_id):
    """
    Obtém registros de ponto do funcionário no dia atual.

    Args:
        funcionario_id (int): ID do funcionário

    Returns:
        list: Lista de registros do dia
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        cursor.execute("""
            SELECT
                tipo_registro,
                TIME(data_hora) as hora
            FROM registros_ponto
            WHERE funcionario_id = %s
            AND DATE(data_hora) = CURDATE()
            ORDER BY data_hora ASC
        """, (funcionario_id,))

        registros = cursor.fetchall()
        conn.close()

        return [{'tipo': r['tipo_registro'], 'hora': str(r['hora'])} for r in registros]

    except Exception as e:
        logger.error(f"Erro ao obter registros do funcionário hoje: {e}")
        return []

# ========================================
# FIM DO BLUEPRINT REGISTRO DE PONTO
# ========================================