#!/usr/bin/env python3
"""
Script para reiniciar o servidor Flask
"""

import paramiko
import time
from datetime import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def restart_flask():
    """Reinicia o servidor Flask"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    log_message("🔄 REINICIANDO SERVIDOR FLASK")
    log_message("=" * 40)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        log_message("🔐 Conectando ao servidor...")
        ssh.connect(hostname, username=username, password=password)
        log_message("✅ Conectado com sucesso!")
        
        # 1. Parar todos os processos Python
        log_message("🛑 Parando processos Python...")
        stdin, stdout, stderr = ssh.exec_command('pkill -f "python.*app.py"')
        time.sleep(3)
        
        # 2. Verificar se ainda há processos rodando
        stdin, stdout, stderr = ssh.exec_command('pgrep -f "python.*app.py"')
        running_processes = stdout.read().decode().strip()
        
        if running_processes:
            log_message(f"⚠️ Ainda há processos rodando: {running_processes}")
            log_message("🔨 Forçando parada...")
            stdin, stdout, stderr = ssh.exec_command('pkill -9 -f "python.*app.py"')
            time.sleep(2)
        else:
            log_message("✅ Todos os processos Python parados")
        
        # 3. Verificar se a porta 5000 está livre
        log_message("🔍 Verificando porta 5000...")
        stdin, stdout, stderr = ssh.exec_command('netstat -tlnp | grep :5000 || echo "Porta livre"')
        port_status = stdout.read().decode().strip()
        log_message(f"📊 Status da porta: {port_status}")
        
        # 4. Iniciar o Flask
        log_message("🚀 Iniciando Flask...")
        stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && nohup python3 app.py > app.log 2>&1 &')
        time.sleep(5)
        
        # 5. Verificar se o processo está rodando
        stdin, stdout, stderr = ssh.exec_command('pgrep -f "python.*app.py"')
        new_process = stdout.read().decode().strip()
        
        if new_process:
            log_message(f"✅ Flask iniciado (PID: {new_process})")
        else:
            log_message("❌ Falha ao iniciar Flask")
            
            # Verificar logs de erro
            log_message("📋 Verificando logs...")
            stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && tail -20 app.log')
            logs = stdout.read().decode()
            log_message(f"📄 Logs:\n{logs}")
            return False
        
        # 6. Testar conectividade
        log_message("🌐 Testando conectividade...")
        time.sleep(3)
        
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000')
        http_status = stdout.read().decode().strip()
        
        if http_status in ['200', '302']:
            log_message(f"✅ Servidor respondendo (Status: {http_status})")
        else:
            log_message(f"⚠️ Status HTTP: {http_status}")
        
        ssh.close()
        
        log_message("🎉 REINICIALIZAÇÃO CONCLUÍDA!")
        log_message("🌐 Teste em: http://************:5000")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Erro: {str(e)}")
        return False

if __name__ == "__main__":
    restart_flask()
