#!/usr/bin/env python3
"""
Script para limpar completamente o banco de dados
Remove todos os registros de ponto e funcionários desligados
"""

import paramiko
import sys
import time
from datetime import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def limpar_banco_dados():
    """Limpa completamente o banco de dados"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    # Credenciais do MySQL
    mysql_user = 'cavalcrod'
    mysql_password = '200381'
    mysql_database = 'controle_ponto'
    
    log_message("🗑️ LIMPEZA COMPLETA DO BANCO DE DADOS")
    log_message("=" * 60)
    log_message("⚠️ ATENÇÃO: Esta operação irá apagar TODOS os dados!")
    log_message("📋 Dados que serão removidos:")
    log_message("   - Todos os registros de ponto")
    log_message("   - Todos os funcionários desligados")
    log_message("   - Banco de horas")
    log_message("   - Histórico de alterações")
    log_message("   - Justificativas de ponto")
    log_message("   - Alertas de ponto")
    log_message("=" * 60)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        log_message("🔐 Conectando ao servidor...")
        ssh.connect(hostname, username=username, password=password)
        log_message("✅ Conectado com sucesso!")
        
        # Criar script SQL de limpeza
        sql_script = f"""
-- SCRIPT DE LIMPEZA COMPLETA DO BANCO DE DADOS
-- Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- ATENÇÃO: Esta operação é IRREVERSÍVEL!

USE {mysql_database};

-- Desabilitar verificações de chave estrangeira temporariamente
SET FOREIGN_KEY_CHECKS = 0;

-- 1. LIMPAR REGISTROS DE PONTO
DELETE FROM registros_ponto;
ALTER TABLE registros_ponto AUTO_INCREMENT = 1;

-- 2. LIMPAR FUNCIONÁRIOS DESLIGADOS
DELETE FROM funcionarios_desligados;
ALTER TABLE funcionarios_desligados AUTO_INCREMENT = 1;

-- 3. LIMPAR BANCO DE HORAS
DELETE FROM banco_horas;
ALTER TABLE banco_horas AUTO_INCREMENT = 1;

-- 4. LIMPAR HISTÓRICO DE ALTERAÇÕES DE PONTO
DELETE FROM historico_alteracoes_ponto;
ALTER TABLE historico_alteracoes_ponto AUTO_INCREMENT = 1;

-- 5. LIMPAR JUSTIFICATIVAS DE PONTO
DELETE FROM justificativas_ponto;
ALTER TABLE justificativas_ponto AUTO_INCREMENT = 1;

-- 6. LIMPAR ALERTAS DE PONTO
DELETE FROM alertas_ponto;
ALTER TABLE alertas_ponto AUTO_INCREMENT = 1;

-- 7. LIMPAR LOG DE DESLIGAMENTOS
DELETE FROM log_desligamentos;
ALTER TABLE log_desligamentos AUTO_INCREMENT = 1;

-- 8. LIMPAR HISTÓRICO DE ALOCAÇÕES
DELETE FROM historico_alocacoes;
ALTER TABLE historico_alocacoes AUTO_INCREMENT = 1;

-- 9. LIMPAR ALOCAÇÕES DE FUNCIONÁRIOS
DELETE FROM funcionario_alocacoes;
ALTER TABLE funcionario_alocacoes AUTO_INCREMENT = 1;

-- 10. LIMPAR ALOCAÇÕES CLIENTE-FUNCIONÁRIO
DELETE FROM funcionario_cliente_alocacao;
ALTER TABLE funcionario_cliente_alocacao AUTO_INCREMENT = 1;

-- Reabilitar verificações de chave estrangeira
SET FOREIGN_KEY_CHECKS = 1;

-- Verificar limpeza
SELECT 'LIMPEZA CONCLUÍDA' as status;
SELECT 
    'registros_ponto' as tabela, 
    COUNT(*) as registros_restantes 
FROM registros_ponto
UNION ALL
SELECT 
    'funcionarios_desligados' as tabela, 
    COUNT(*) as registros_restantes 
FROM funcionarios_desligados
UNION ALL
SELECT 
    'banco_horas' as tabela, 
    COUNT(*) as registros_restantes 
FROM banco_horas;
"""
        
        # Salvar script SQL no servidor
        log_message("📝 Criando script SQL de limpeza...")
        sftp = ssh.open_sftp()
        
        with sftp.open('/tmp/limpar_banco.sql', 'w') as f:
            f.write(sql_script)
        
        log_message("✅ Script SQL criado em /tmp/limpar_banco.sql")
        
        # Fazer backup antes da limpeza
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"/var/www/controle-ponto/backup-build/backup_antes_limpeza_{timestamp}.sql"
        
        log_message("📦 Criando backup antes da limpeza...")
        backup_cmd = f"mysqldump -h localhost -u {mysql_user} -p{mysql_password} {mysql_database} > {backup_file}"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        
        backup_error = stderr.read().decode()
        if backup_error and "Warning" not in backup_error:
            log_message(f"⚠️ Aviso no backup: {backup_error}")
        else:
            log_message("✅ Backup criado com sucesso")
        
        # Executar limpeza
        log_message("🗑️ Executando limpeza do banco de dados...")
        log_message("⚠️ ÚLTIMA CHANCE PARA CANCELAR! (Ctrl+C)")
        time.sleep(3)
        
        cleanup_cmd = f"mysql -h localhost -u {mysql_user} -p{mysql_password} {mysql_database} < /tmp/limpar_banco.sql"
        stdin, stdout, stderr = ssh.exec_command(cleanup_cmd)
        
        cleanup_output = stdout.read().decode()
        cleanup_error = stderr.read().decode()
        
        if cleanup_error and "Warning" not in cleanup_error:
            log_message(f"❌ Erro na limpeza: {cleanup_error}")
            return False
        
        log_message("✅ Limpeza executada com sucesso!")
        
        # Verificar resultados
        log_message("🔍 Verificando resultados da limpeza...")
        
        verify_cmd = f"""mysql -h localhost -u {mysql_user} -p{mysql_password} {mysql_database} -e "
        SELECT 'registros_ponto' as tabela, COUNT(*) as registros FROM registros_ponto
        UNION ALL
        SELECT 'funcionarios_desligados' as tabela, COUNT(*) as registros FROM funcionarios_desligados
        UNION ALL
        SELECT 'banco_horas' as tabela, COUNT(*) as registros FROM banco_horas
        UNION ALL
        SELECT 'funcionarios_ativos' as tabela, COUNT(*) as registros FROM funcionarios WHERE status_cadastro = 'Ativo';
        " """
        
        stdin, stdout, stderr = ssh.exec_command(verify_cmd)
        verify_output = stdout.read().decode()
        
        log_message("📊 Resultados da verificação:")
        log_message(verify_output)
        
        # Limpar arquivo temporário
        stdin, stdout, stderr = ssh.exec_command('rm -f /tmp/limpar_banco.sql')
        
        sftp.close()
        ssh.close()
        
        log_message("🎉 LIMPEZA CONCLUÍDA COM SUCESSO!")
        log_message("=" * 60)
        log_message("✅ Todos os registros de ponto foram removidos")
        log_message("✅ Todos os funcionários desligados foram removidos")
        log_message("✅ Banco de horas limpo")
        log_message("✅ Histórico de alterações limpo")
        log_message("✅ Contadores AUTO_INCREMENT resetados")
        log_message("")
        log_message(f"📦 Backup salvo em: {backup_file}")
        log_message("🌐 Sistema pronto para novos registros")
        log_message("🔄 Reinicie o sistema se necessário")
        
        return True
        
    except KeyboardInterrupt:
        log_message("❌ Operação cancelada pelo usuário")
        return False
    except Exception as e:
        log_message(f"❌ Erro durante a limpeza: {str(e)}")
        return False

def main():
    """Função principal"""
    log_message("RLPONTO-WEB - Limpeza Completa do Banco")
    log_message("Desenvolvido por: AiNexus Tecnologia")
    log_message("")
    
    # Confirmação de segurança
    print("⚠️ ATENÇÃO: Esta operação irá apagar TODOS os registros de ponto!")
    print("⚠️ Esta operação é IRREVERSÍVEL!")
    print("")
    confirmacao = input("Digite 'CONFIRMAR LIMPEZA' para continuar: ")
    
    if confirmacao != "CONFIRMAR LIMPEZA":
        print("❌ Operação cancelada. Texto de confirmação incorreto.")
        return False
    
    print("")
    if limpar_banco_dados():
        log_message("✅ Limpeza realizada com sucesso!")
        return True
    else:
        log_message("❌ Falha na limpeza!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
