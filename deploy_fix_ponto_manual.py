#!/usr/bin/env python3
"""
Script para fazer deploy da correção do ponto manual
"""

import paramiko
import os
import sys
import time
from datetime import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def deploy_fix():
    """Faz deploy da correção"""
    
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    log_message("🚀 INICIANDO DEPLOY DA CORREÇÃO DO PONTO MANUAL")
    log_message("=" * 60)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        log_message("🔐 Conectando ao servidor...")
        ssh.connect(hostname, username=username, password=password)
        log_message("✅ Conectado com sucesso!")
        
        # Timestamp para backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. Fazer backup do arquivo atual
        log_message("📦 Fazendo backup do arquivo atual...")
        backup_cmd = f"cp /var/www/controle-ponto/app_registro_ponto.py /var/www/controle-ponto/backup-build/app_registro_ponto_backup_fix_ponto_manual_{timestamp}.py"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        
        if stderr.read():
            log_message("⚠️ Aviso no backup, mas continuando...")
        else:
            log_message("✅ Backup criado com sucesso")
        
        # 2. Transferir arquivo corrigido
        log_message("📤 Transferindo arquivo corrigido...")
        sftp = ssh.open_sftp()
        
        local_file = 'var/www/controle-ponto/app_registro_ponto.py'
        remote_file = '/var/www/controle-ponto/app_registro_ponto.py'
        
        if not os.path.exists(local_file):
            log_message(f"❌ Arquivo local não encontrado: {local_file}")
            return False
        
        sftp.put(local_file, remote_file)
        log_message("✅ Arquivo transferido com sucesso")
        
        # 3. Verificar se o arquivo foi transferido
        log_message("🔍 Verificando arquivo transferido...")
        stdin, stdout, stderr = ssh.exec_command(f'ls -la {remote_file}')
        file_info = stdout.read().decode()
        log_message(f"📄 Arquivo: {file_info.strip()}")
        
        # 4. Verificar sintaxe Python
        log_message("🐍 Verificando sintaxe Python...")
        stdin, stdout, stderr = ssh.exec_command(f'cd /var/www/controle-ponto && python3 -m py_compile app_registro_ponto.py')
        syntax_error = stderr.read().decode()
        
        if syntax_error:
            log_message(f"❌ Erro de sintaxe: {syntax_error}")
            return False
        else:
            log_message("✅ Sintaxe Python válida")
        
        # 5. Parar o serviço Flask
        log_message("🛑 Parando serviço Flask...")
        stdin, stdout, stderr = ssh.exec_command('pkill -f "python.*app.py" || echo "Nenhum processo Flask encontrado"')
        time.sleep(3)
        
        # 6. Iniciar o serviço Flask
        log_message("🚀 Iniciando serviço Flask...")
        stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && nohup python3 app.py > app.log 2>&1 &')
        time.sleep(5)
        
        # 7. Verificar se o serviço está rodando
        log_message("🔍 Verificando se o serviço está rodando...")
        stdin, stdout, stderr = ssh.exec_command('pgrep -f "python.*app.py"')
        process_id = stdout.read().decode().strip()
        
        if process_id:
            log_message(f"✅ Flask está rodando (PID: {process_id})")
        else:
            log_message("❌ Flask não está rodando")
            
            # Verificar logs de erro
            log_message("📋 Verificando logs de erro...")
            stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && tail -20 app.log')
            logs = stdout.read().decode()
            log_message(f"📄 Últimos logs:\n{logs}")
            return False
        
        # 8. Testar conectividade HTTP
        log_message("🌐 Testando conectividade HTTP...")
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000')
        http_status = stdout.read().decode().strip()
        
        if http_status in ['200', '302']:
            log_message(f"✅ Servidor HTTP respondendo (Status: {http_status})")
        else:
            log_message(f"⚠️ Status HTTP inesperado: {http_status}")
        
        # 9. Transferir e executar script de teste
        log_message("🧪 Transferindo script de teste...")
        test_script_local = 'test_ponto_manual_fix.py'
        test_script_remote = '/var/www/controle-ponto/test_ponto_manual_fix.py'
        
        if os.path.exists(test_script_local):
            sftp.put(test_script_local, test_script_remote)
            log_message("✅ Script de teste transferido")
            
            # Executar teste
            log_message("🧪 Executando teste da API...")
            stdin, stdout, stderr = ssh.exec_command(f'cd /var/www/controle-ponto && python3 {test_script_remote}')
            test_output = stdout.read().decode()
            test_error = stderr.read().decode()
            
            log_message("📋 Resultado do teste:")
            log_message(test_output)
            
            if test_error:
                log_message(f"⚠️ Erros no teste: {test_error}")
        
        sftp.close()
        ssh.close()
        
        log_message("🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
        log_message("=" * 60)
        log_message("✅ Arquivo app_registro_ponto.py atualizado")
        log_message("✅ Serviço Flask reiniciado")
        log_message("✅ Sistema funcionando")
        log_message("")
        log_message("🌐 Teste o ponto manual em: http://************:5000/registro-ponto/manual")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Erro durante o deploy: {str(e)}")
        return False

def main():
    """Função principal"""
    log_message("RLPONTO-WEB - Deploy de Correção do Ponto Manual")
    log_message("Desenvolvido por: AiNexus Tecnologia")
    log_message("")
    
    if deploy_fix():
        log_message("✅ Deploy realizado com sucesso!")
        return True
    else:
        log_message("❌ Falha no deploy!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
