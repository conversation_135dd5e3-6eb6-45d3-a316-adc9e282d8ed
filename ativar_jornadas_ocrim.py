#!/usr/bin/env python3
"""
Script para ativar jornadas da empresa Ocrim se estiverem inativas
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager

def ativar_jornadas_ocrim():
    """Ativar jornadas da empresa Ocrim"""
    print("🔧 ATIVANDO JORNADAS DA EMPRESA OCRIM")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar empresa Ocrim
        print("\n1. Buscando empresa Ocrim...")
        sql_empresa = """
        SELECT id, razao_social, nome_fantasia
        FROM empresas 
        WHERE razao_social LIKE '%Ocrim%' OR nome_fantasia LIKE '%Ocrim%'
        """
        empresas = db.execute_query(sql_empresa)
        
        if not empresas:
            print("❌ Empresa Ocrim não encontrada!")
            return False
        
        empresa_ocrim = empresas[0]
        print(f"✅ Empresa encontrada: {empresa_ocrim['razao_social']} (ID: {empresa_ocrim['id']})")
        
        # 2. Verificar jornadas existentes
        print(f"\n2. Verificando jornadas da empresa...")
        sql_jornadas = """
        SELECT id, nome_jornada, ativa, padrao
        FROM jornadas_trabalho
        WHERE empresa_id = %s
        ORDER BY padrao DESC, nome_jornada
        """
        jornadas = db.execute_query(sql_jornadas, (empresa_ocrim['id'],))
        
        if not jornadas:
            print("❌ Nenhuma jornada encontrada para a empresa Ocrim!")
            print("💡 Você precisa cadastrar jornadas em Configurações > Jornadas de Trabalho")
            return False
        
        print(f"✅ Encontradas {len(jornadas)} jornadas:")
        jornadas_inativas = []
        
        for jornada in jornadas:
            status = "ATIVA" if jornada['ativa'] else "INATIVA"
            padrao = " (PADRÃO)" if jornada['padrao'] else ""
            print(f"   - {jornada['nome_jornada']}: {status}{padrao}")
            
            if not jornada['ativa']:
                jornadas_inativas.append(jornada)
        
        # 3. Ativar jornadas inativas
        if jornadas_inativas:
            print(f"\n3. Ativando {len(jornadas_inativas)} jornadas inativas...")
            
            for jornada in jornadas_inativas:
                sql_ativar = """
                UPDATE jornadas_trabalho 
                SET ativa = TRUE, data_atualizacao = NOW()
                WHERE id = %s
                """
                
                resultado = db.execute_query(sql_ativar, (jornada['id'],))
                
                if resultado:
                    print(f"   ✅ Ativada: {jornada['nome_jornada']}")
                else:
                    print(f"   ❌ Erro ao ativar: {jornada['nome_jornada']}")
            
            print(f"\n✅ Processo de ativação concluído!")
            
        else:
            print(f"\n✅ Todas as jornadas já estão ativas!")
        
        # 4. Verificar resultado final
        print(f"\n4. Verificando resultado final...")
        jornadas_finais = db.execute_query(sql_jornadas, (empresa_ocrim['id'],))
        jornadas_ativas_final = [j for j in jornadas_finais if j['ativa']]
        
        print(f"✅ Total de jornadas ativas: {len(jornadas_ativas_final)}")
        
        if jornadas_ativas_final:
            print("✅ A empresa Ocrim agora tem jornadas ativas!")
            print("✅ A mensagem de fallback não deve mais aparecer!")
        else:
            print("❌ Ainda não há jornadas ativas!")
        
        return len(jornadas_ativas_final) > 0
        
    except Exception as e:
        print(f"❌ Erro durante ativação: {e}")
        return False

def verificar_api_apos_correcao():
    """Verificar se a API agora funciona corretamente"""
    print(f"\n" + "=" * 60)
    print("🧪 TESTANDO API APÓS CORREÇÃO")
    print("=" * 60)
    
    try:
        import requests
        
        # Testar algumas empresas
        base_url = "http://************"
        
        for empresa_id in [1, 2, 3, 4, 5]:
            print(f"\n📊 Testando empresa ID: {empresa_id}")
            
            url = f"{base_url}/empresa-principal/api/jornadas-empresa/{empresa_id}"
            
            try:
                response = requests.get(url, timeout=5)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('success'):
                            jornadas = data.get('jornadas', [])
                            print(f"   ✅ {len(jornadas)} jornadas encontradas")
                            
                            if data.get('warning'):
                                print(f"   ⚠️ Warning: {data['warning']}")
                            
                            if jornadas and 'Ocrim' in str(jornadas[0]):
                                print(f"   🎯 EMPRESA OCRIM ENCONTRADA!")
                                
                        elif data.get('critical_error'):
                            print(f"   ❌ Erro crítico: {data.get('message', 'N/A')}")
                            if data.get('error_type') == 'jornadas_inativas':
                                print(f"   💡 Tipo: Jornadas inativas detectadas")
                        else:
                            print(f"   ❌ Erro: {data.get('message', 'N/A')}")
                            
                    except:
                        print(f"   ❌ Resposta não é JSON")
                        
            except requests.RequestException as e:
                print(f"   ❌ Erro de conexão: {e}")
                
    except ImportError:
        print("⚠️ Módulo requests não disponível - pule este teste")

if __name__ == "__main__":
    print("🚀 INICIANDO CORREÇÃO DAS JORNADAS DA EMPRESA OCRIM")
    print("=" * 60)
    
    sucesso = ativar_jornadas_ocrim()
    
    if sucesso:
        verificar_api_apos_correcao()
        
        print(f"\n" + "=" * 60)
        print("🎉 CORREÇÃO CONCLUÍDA!")
        print("=" * 60)
        print("✅ As jornadas da empresa Ocrim foram ativadas")
        print("✅ A mensagem de fallback não deve mais aparecer")
        print("✅ Teste a funcionalidade de alocação de funcionários")
        print("=" * 60)
    else:
        print(f"\n" + "=" * 60)
        print("❌ CORREÇÃO NÃO CONCLUÍDA")
        print("=" * 60)
        print("💡 Verifique se a empresa Ocrim existe")
        print("💡 Cadastre jornadas se necessário")
        print("=" * 60)
