#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da API de Registro Manual
Testa diretamente a função que estava falhando
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

from utils.database import get_db_connection
from utils.config import Config
from pymysql.cursors import DictCursor
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_registro_manual():
    """Testa a função de registro manual diretamente"""
    try:
        # Testar conexão
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        print("✅ Conexão com banco estabelecida")
        
        # Testar se a tabela registros_ponto existe e sua estrutura
        cursor.execute("DESCRIBE registros_ponto")
        colunas = cursor.fetchall()
        
        print("\n📋 Estrutura da tabela registros_ponto:")
        for coluna in colunas:
            print(f"  - {coluna['Field']}: {coluna['Type']}")
        
        # Verificar se existem funcionários
        cursor.execute("SELECT id, nome_completo FROM funcionarios WHERE status_cadastro = 'Ativo' LIMIT 5")
        funcionarios = cursor.fetchall()
        
        print(f"\n👥 Funcionários ativos encontrados: {len(funcionarios)}")
        for func in funcionarios:
            print(f"  - ID {func['id']}: {func['nome_completo']}")
        
        # Testar se a tabela banco_horas existe
        cursor.execute("DESCRIBE banco_horas")
        colunas_banco = cursor.fetchall()
        
        print(f"\n🏦 Estrutura da tabela banco_horas:")
        for coluna in colunas_banco:
            print(f"  - {coluna['Field']}: {coluna['Type']}")
        
        # Verificar registros recentes
        cursor.execute("""
            SELECT COUNT(*) as total, 
                   DATE(data_hora) as data,
                   funcionario_id
            FROM registros_ponto 
            WHERE data_hora >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
            GROUP BY DATE(data_hora), funcionario_id
            ORDER BY data DESC
            LIMIT 10
        """)
        registros_recentes = cursor.fetchall()
        
        print(f"\n📊 Registros recentes (últimos 7 dias):")
        for reg in registros_recentes:
            print(f"  - Funcionário {reg['funcionario_id']}: {reg['total']} registros em {reg['data']}")
        
        conn.close()
        print("\n✅ Teste concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Iniciando teste da API de registro manual...")
    success = test_registro_manual()
    
    if success:
        print("\n🎉 Todos os testes passaram!")
    else:
        print("\n💥 Falha nos testes!")
        sys.exit(1)
