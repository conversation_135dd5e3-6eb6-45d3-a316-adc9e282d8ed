#!/usr/bin/env python3
"""
Debug para verificar jornadas da empresa Ocrim
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager

def debug_jornadas_ocrim():
    """Debug das jornadas da empresa Ocrim"""
    print("🔍 DEBUG: JORNADAS DA EMPRESA OCRIM")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar empresa Ocrim
        print("\n1. Buscando empresa Ocrim...")
        sql_empresa = """
        SELECT id, razao_social, nome_fantasia, empresa_principal
        FROM empresas 
        WHERE razao_social LIKE '%Ocrim%' OR nome_fantasia LIKE '%Ocrim%'
        """
        empresas = db.execute_query(sql_empresa)
        
        if not empresas:
            print("❌ Empresa Ocrim não encontrada!")
            return False
        
        empresa_ocrim = empresas[0]
        print(f"✅ Empresa encontrada:")
        print(f"   ID: {empresa_ocrim['id']}")
        print(f"   Razão Social: {empresa_ocrim['razao_social']}")
        print(f"   Nome Fantasia: {empresa_ocrim['nome_fantasia']}")
        print(f"   É Empresa Principal: {'Sim' if empresa_ocrim['empresa_principal'] else 'Não'}")
        
        # 2. Buscar jornadas da empresa Ocrim
        print(f"\n2. Buscando jornadas da empresa Ocrim (ID: {empresa_ocrim['id']})...")
        sql_jornadas = """
        SELECT id, nome_jornada, tipo_jornada, categoria_funcionario,
               seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
               intervalo_inicio, intervalo_fim, tolerancia_entrada_minutos,
               padrao, ativa, data_cadastro, data_atualizacao
        FROM jornadas_trabalho
        WHERE empresa_id = %s
        ORDER BY padrao DESC, ativa DESC, nome_jornada
        """
        jornadas = db.execute_query(sql_jornadas, (empresa_ocrim['id'],))
        
        if jornadas:
            print(f"✅ Encontradas {len(jornadas)} jornadas:")
            for i, jornada in enumerate(jornadas, 1):
                status = []
                if jornada['padrao']:
                    status.append("PADRÃO")
                if jornada['ativa']:
                    status.append("ATIVA")
                else:
                    status.append("INATIVA")
                
                print(f"\n   {i}. {jornada['nome_jornada']}")
                print(f"      ID: {jornada['id']}")
                print(f"      Status: {' | '.join(status)}")
                print(f"      Tipo: {jornada['tipo_jornada']}")
                print(f"      Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
                print(f"      Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
                if jornada['intervalo_inicio'] and jornada['intervalo_fim']:
                    print(f"      Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
                print(f"      Cadastro: {jornada['data_cadastro']}")
        else:
            print("❌ Nenhuma jornada encontrada para a empresa Ocrim!")
        
        # 3. Testar a mesma query que a API usa (simplificada)
        print(f"\n3. Testando query da API...")
        sql_api = """
        SELECT j.id, j.nome_jornada as nome, j.ativa, j.padrao, e.razao_social as empresa_nome
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE AND j.empresa_id = %s
        ORDER BY j.padrao DESC, j.nome_jornada
        """

        jornadas_api = db.execute_query(sql_api, (empresa_ocrim['id'],))
        
        if jornadas_api:
            print(f"✅ API retornaria {len(jornadas_api)} jornadas ativas:")
            for jornada in jornadas_api:
                print(f"   - {jornada['nome']} (Empresa: {jornada['empresa_nome']})")
        else:
            print("❌ API não retornaria nenhuma jornada ativa!")
        
        # 4. Verificar empresa principal
        print(f"\n4. Verificando empresa principal...")
        sql_principal = """
        SELECT id, razao_social, nome_fantasia
        FROM empresas 
        WHERE empresa_principal = TRUE
        """
        empresa_principal = db.execute_query(sql_principal)
        
        if empresa_principal:
            emp_principal = empresa_principal[0]
            print(f"✅ Empresa principal: {emp_principal['razao_social']} (ID: {emp_principal['id']})")
            
            # Verificar jornadas da empresa principal
            jornadas_principal = db.execute_query(sql_api, (emp_principal['id'],))
            if jornadas_principal:
                print(f"✅ Empresa principal tem {len(jornadas_principal)} jornadas ativas")
            else:
                print("❌ Empresa principal não tem jornadas ativas!")
        else:
            print("❌ Nenhuma empresa principal definida!")
        
        # 5. Conclusão
        print(f"\n" + "=" * 60)
        print("📊 ANÁLISE DO PROBLEMA:")
        print("=" * 60)
        
        if jornadas_api:
            print("✅ A empresa Ocrim TEM jornadas ativas cadastradas")
            print("❌ O problema está na lógica da API que está usando fallback incorretamente")
            print("🔧 SOLUÇÃO: Corrigir a lógica da API para não mostrar warning quando há jornadas")
        else:
            print("❌ A empresa Ocrim NÃO tem jornadas ativas")
            if jornadas:
                print("⚠️ Existem jornadas cadastradas mas estão INATIVAS")
                print("🔧 SOLUÇÃO: Ativar as jornadas da empresa Ocrim")
            else:
                print("❌ Não existem jornadas cadastradas para a empresa")
                print("🔧 SOLUÇÃO: Cadastrar jornadas para a empresa Ocrim")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        return False

if __name__ == "__main__":
    debug_jornadas_ocrim()
