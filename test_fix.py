#!/usr/bin/env python3

"""
Teste direto da correção implementada
"""

import pymysql
from datetime import datetime

def test_banco_horas_insert():
    """Testa se a inserção na tabela banco_horas funciona"""
    try:
        # Conectar ao banco
        conn = pymysql.connect(
            host='************',
            user='cavalcrod', 
            password='200381',
            database='controle_ponto',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        
        # Verificar se a tabela banco_horas existe
        cursor.execute("SHOW TABLES LIKE 'banco_horas'")
        if not cursor.fetchone():
            print("❌ Tabela banco_horas não existe!")
            return False
        
        print("✅ Tabela banco_horas existe")
        
        # Verificar estrutura
        cursor.execute("DESCRIBE banco_horas")
        columns = cursor.fetchall()
        print("\n📋 Estrutura da tabela banco_horas:")
        for col in columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # Testar inserção (simulando a correção)
        funcionario_id = 1  # Usar funcionário existente
        data_referencia = datetime.now().date()
        
        # Verificar se funcionário existe
        cursor.execute("SELECT id, nome_completo FROM funcionarios WHERE id = %s", (funcionario_id,))
        funcionario = cursor.fetchone()
        
        if not funcionario:
            print(f"❌ Funcionário ID {funcionario_id} não encontrado!")
            return False
        
        print(f"✅ Funcionário encontrado: {funcionario[1]}")
        
        # Testar a query de inserção/atualização
        try:
            cursor.execute("""
                INSERT INTO banco_horas 
                (funcionario_id, data_referencia, horas_extras_minutos, 
                 saldo_credor_minutos, saldo_liquido_minutos, status_dia, observacoes)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    horas_extras_minutos = VALUES(horas_extras_minutos),
                    saldo_credor_minutos = VALUES(saldo_credor_minutos),
                    saldo_liquido_minutos = VALUES(saldo_liquido_minutos),
                    status_dia = VALUES(status_dia),
                    observacoes = VALUES(observacoes)
            """, (
                funcionario_id,
                data_referencia,
                60,  # 1 hora extra em minutos
                60,  # 1 hora de crédito
                120, # 2 horas total
                'completo',
                'Teste de correção - Horas trabalhadas: 8.00h'
            ))
            
            print("✅ Query de inserção executada com sucesso!")
            
            # Verificar se foi inserido
            cursor.execute("""
                SELECT * FROM banco_horas 
                WHERE funcionario_id = %s AND data_referencia = %s
            """, (funcionario_id, data_referencia))
            
            resultado = cursor.fetchone()
            if resultado:
                print("✅ Registro inserido/atualizado com sucesso!")
                print(f"   - Horas extras: {resultado[4]} minutos")
                print(f"   - Saldo credor: {resultado[6]} minutos")
                print(f"   - Status: {resultado[8]}")
            else:
                print("❌ Registro não foi inserido!")
                return False
            
            # Rollback para não afetar dados reais
            conn.rollback()
            print("✅ Rollback executado - dados não foram persistidos")
            
        except Exception as e:
            print(f"❌ Erro na inserção: {e}")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testando correção da tabela banco_horas...")
    
    if test_banco_horas_insert():
        print("\n🎉 Correção funcionando corretamente!")
        print("✅ O problema do registro manual foi resolvido!")
    else:
        print("\n💥 Correção ainda tem problemas!")
