#!/usr/bin/env python3

"""
Script simples para iniciar o Flask e testar a correção
"""

import sys
import os

# Adicionar o diretório do app ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

try:
    print("🚀 Iniciando aplicação Flask...")
    
    # Importar e executar o app
    from app import app, Config
    
    print("✅ App importado com sucesso!")
    print(f"📊 Debug mode: {Config.FLASK_DEBUG}")
    print(f"🌐 Host: {Config.FLASK_HOST}")
    print(f"🔌 Port: {Config.FLASK_PORT}")
    
    # Iniciar o servidor
    print("\n🔥 Iniciando servidor Flask...")
    app.run(
        host=Config.FLASK_HOST,
        port=Config.FLASK_PORT,
        debug=Config.FLASK_DEBUG,
        use_reloader=False  # Evitar problemas com reloader
    )
    
except Exception as e:
    print(f"❌ Erro ao iniciar Flask: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
