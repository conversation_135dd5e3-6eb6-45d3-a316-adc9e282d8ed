#!/usr/bin/env python3
"""
Script para testar e corrigir problemas do ponto manual
"""

import sys
import os
import requests
import json
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_api_registrar_manual():
    """Testa a API de registro manual"""
    print("🧪 TESTANDO API DE REGISTRO MANUAL")
    print("=" * 50)
    
    # URL da API
    base_url = "http://************:5000"
    api_url = f"{base_url}/registro-ponto/api/registrar-manual"
    
    # Dados de teste
    test_data = {
        'funcionario_id': '1',  # ID de teste
        'tipo_registro': 'entrada_manha',
        'observacoes': 'Teste de correção do ponto manual'
    }
    
    print(f"📡 URL: {api_url}")
    print(f"📋 Dados: {test_data}")
    
    try:
        # Fazer requisição POST
        response = requests.post(
            api_url,
            data=test_data,
            timeout=30,
            headers={
                'User-Agent': 'RLPONTO-WEB Test Script',
                'Accept': 'application/json'
            }
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Headers: {dict(response.headers)}")
        
        # Tentar decodificar JSON
        try:
            result = response.json()
            print(f"✅ Resposta JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print("🎉 SUCESSO: API funcionando corretamente!")
                return True
            else:
                print(f"❌ ERRO: {result.get('message', 'Erro desconhecido')}")
                return False
                
        except json.JSONDecodeError:
            print(f"❌ ERRO: Resposta não é JSON válido")
            print(f"📄 Conteúdo: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERRO: Não foi possível conectar ao servidor")
        print("🔍 Verifique se o servidor está rodando em http://************:5000")
        return False
    except requests.exceptions.Timeout:
        print("❌ ERRO: Timeout na requisição")
        return False
    except Exception as e:
        print(f"❌ ERRO INESPERADO: {e}")
        return False

def check_server_status():
    """Verifica se o servidor está rodando"""
    print("🔍 VERIFICANDO STATUS DO SERVIDOR")
    print("=" * 40)
    
    base_url = "http://************:5000"
    
    try:
        response = requests.get(base_url, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Servidor está rodando")
            return True
        elif response.status_code == 302:
            print("✅ Servidor está rodando (redirecionamento para login)")
            return True
        else:
            print(f"⚠️ Servidor respondeu com status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Servidor não está respondendo")
        return False
    except Exception as e:
        print(f"❌ Erro ao verificar servidor: {e}")
        return False

def check_database_structure():
    """Verifica estrutura da tabela registros_ponto"""
    print("🗄️ VERIFICANDO ESTRUTURA DO BANCO")
    print("=" * 40)
    
    try:
        # Adicionar caminho do sistema
        sys.path.insert(0, '/var/www/controle-ponto')
        
        from utils.database import get_db_connection
        from pymysql.cursors import DictCursor
        
        conn = get_db_connection()
        if not conn:
            print("❌ Erro ao conectar com o banco")
            return False
            
        cursor = conn.cursor(DictCursor)
        
        # Verificar se tabela existe
        cursor.execute("SHOW TABLES LIKE 'registros_ponto'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Tabela 'registros_ponto' não existe")
            return False
        
        print("✅ Tabela 'registros_ponto' existe")
        
        # Verificar estrutura da tabela
        cursor.execute("DESCRIBE registros_ponto")
        columns = cursor.fetchall()
        
        print("📋 Colunas da tabela:")
        required_columns = ['id', 'funcionario_id', 'tipo_registro', 'data_hora', 'metodo_registro']
        
        existing_columns = [col['Field'] for col in columns]
        
        for col in required_columns:
            if col in existing_columns:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} - AUSENTE")
        
        # Verificar se há funcionários para teste
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo'")
        funcionarios_count = cursor.fetchone()
        
        print(f"👥 Funcionários ativos: {funcionarios_count['total']}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 DIAGNÓSTICO E TESTE DO PONTO MANUAL")
    print("=" * 60)
    print("Sistema: RLPONTO-WEB")
    print("Servidor: ************:5000")
    print("=" * 60)
    
    # 1. Verificar servidor
    if not check_server_status():
        print("\n❌ FALHA: Servidor não está respondendo")
        return False
    
    # 2. Verificar banco de dados
    if not check_database_structure():
        print("\n❌ FALHA: Problemas na estrutura do banco")
        return False
    
    # 3. Testar API
    if not test_api_registrar_manual():
        print("\n❌ FALHA: API não está funcionando corretamente")
        return False
    
    print("\n🎉 SUCESSO: Todos os testes passaram!")
    print("✅ O sistema de ponto manual está funcionando")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
