-- Script SQL para ativar jornadas da empresa Ocrim

-- 1. Verificar empresa Ocrim
SELECT 'EMPRESA OCRIM:' as info;
SELECT id, razao_social, nome_fantasia 
FROM empresas 
WHERE razao_social LIKE '%Ocrim%' OR nome_fantasia LIKE '%Ocrim%';

-- 2. Verificar jornadas existentes da Ocrim
SELECT 'JORNADAS EXISTENTES:' as info;
SELECT j.id, j.nome_jornada, j.ativa, j.padrao, e.razao_social
FROM jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
WHERE e.razao_social LIKE '%Ocrim%' OR e.nome_fantasia LIKE '%Ocrim%'
ORDER BY j.padrao DESC, j.nome_jornada;

-- 3. Ativar todas as jornadas da empresa Ocrim
SELECT 'ATIVANDO JORNADAS...' as info;
UPDATE jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
SET j.ativa = TRUE, j.data_atualizacao = NOW()
WHERE (e.razao_social LIKE '%Ocrim%' OR e.nome_fantasia LIKE '%Ocrim%')
AND j.ativa = FALSE;

-- 4. Verificar resultado
SELECT 'RESULTADO FINAL:' as info;
SELECT j.id, j.nome_jornada, j.ativa, j.padrao, e.razao_social
FROM jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
WHERE e.razao_social LIKE '%Ocrim%' OR e.nome_fantasia LIKE '%Ocrim%'
ORDER BY j.padrao DESC, j.nome_jornada;

-- 5. Contar jornadas ativas
SELECT 'RESUMO:' as info;
SELECT 
    e.razao_social,
    COUNT(*) as total_jornadas,
    SUM(CASE WHEN j.ativa = TRUE THEN 1 ELSE 0 END) as jornadas_ativas
FROM jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
WHERE e.razao_social LIKE '%Ocrim%' OR e.nome_fantasia LIKE '%Ocrim%'
GROUP BY e.id, e.razao_social;
